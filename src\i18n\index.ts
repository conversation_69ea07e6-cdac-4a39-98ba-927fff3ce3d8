import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// English namespaces
import enCommon from './locales/en/common.json';
import enAuth from './locales/en/auth.json';
import enEncryption from './locales/en/encryption.json';
import enOtp from './locales/en/otp.json';
import enProfile from './locales/en/profile.json';
import enSettings from './locales/en/settings.json';
import enPublic from './locales/en/public.json';
import enPricing from './locales/en/pricing.json';
import enContact from './locales/en/contact.json';
import enFaqs from './locales/en/faqs.json';
import enPrivacy from './locales/en/privacy.json';
import enTerms from './locales/en/terms.json';
import enHelp from './locales/en/help.json';
import enFeatures from './locales/en/features.json';
import enChangelog from './locales/en/changelog.json';
import enToast from './locales/en/toast.json';

// French namespaces
import frCommon from './locales/fr/common.json';
import frAuth from './locales/fr/auth.json';
import frEncryption from './locales/fr/encryption.json';
import frOtp from './locales/fr/otp.json';
import frProfile from './locales/fr/profile.json';
import frSettings from './locales/fr/settings.json';
import frPublic from './locales/fr/public.json';
import frPricing from './locales/fr/pricing.json';
import frContact from './locales/fr/contact.json';
import frFaqs from './locales/fr/faqs.json';
import frPrivacy from './locales/fr/privacy.json';
import frTerms from './locales/fr/terms.json';
import frHelp from './locales/fr/help.json';
import frFeatures from './locales/fr/features.json';
import frChangelog from './locales/fr/changelog.json';
import frToast from './locales/fr/toast.json';

// Spanish namespaces
import esCommon from './locales/es/common.json';
import esAuth from './locales/es/auth.json';
import esEncryption from './locales/es/encryption.json';
import esOtp from './locales/es/otp.json';
import esProfile from './locales/es/profile.json';
import esSettings from './locales/es/settings.json';
import esPublic from './locales/es/public.json';
import esPricing from './locales/es/pricing.json';
import esContact from './locales/es/contact.json';
import esFaqs from './locales/es/faqs.json';
import esPrivacy from './locales/es/privacy.json';
import esTerms from './locales/es/terms.json';
import esHelp from './locales/es/help.json';
import esFeatures from './locales/es/features.json';
import esChangelog from './locales/es/changelog.json';
import esToast from './locales/es/toast.json';

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        common: enCommon,
        auth: enAuth,
        encryption: enEncryption,
        otp: enOtp,
        profile: enProfile,
        settings: enSettings,
        public: enPublic,
        pricing: enPricing,
        contact: enContact,
        faqs: enFaqs,
        privacy: enPrivacy,
        terms: enTerms,
        help: enHelp,
        features: enFeatures,
        changelog: enChangelog,
        toast: enToast
      },
      fr: {
        common: frCommon,
        auth: frAuth,
        encryption: frEncryption,
        otp: frOtp,
        profile: frProfile,
        settings: frSettings,
        public: frPublic,
        pricing: frPricing,
        contact: frContact,
        faqs: frFaqs,
        privacy: frPrivacy,
        terms: frTerms,
        help: frHelp,
        features: frFeatures,
        changelog: frChangelog,
        toast: frToast
      },
      es: {
        common: esCommon,
        auth: esAuth,
        encryption: esEncryption,
        otp: esOtp,
        profile: esProfile,
        settings: esSettings,
        public: esPublic,
        pricing: esPricing,
        contact: esContact,
        faqs: esFaqs,
        privacy: esPrivacy,
        terms: esTerms,
        help: esHelp,
        features: esFeatures,
        changelog: esChangelog,
        toast: esToast
      },
    },
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'auth', 'encryption', 'otp', 'profile', 'settings', 'public', 'pricing', 'contact', 'faqs', 'privacy', 'terms', 'help', 'features', 'changelog', 'toast'],
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;