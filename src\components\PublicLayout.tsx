import React from 'react';
import { useLocation } from 'react-router-dom';
import SEO from './SEO';
import Navigation from './Navigation';
import Footer from './Footer';

interface PublicLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  useSEO?: boolean;
}

const PublicLayout: React.FC<PublicLayoutProps> = ({ 
  children, 
  title, 
  description, 
  useSEO = true 
}) => {
  const location = useLocation();

  // Determine page title based on current path if not provided
  const pageTitle = title || (() => {
    const path = location.pathname;
    if (path === '/') return 'Cloud OTP - Professional OTP Management';
    if (path === '/features') return 'Features - Cloud OTP';
    if (path === '/pricing') return 'Pricing - Cloud OTP';
    if (path === '/contact') return 'Contact - Cloud OTP';
    return 'Cloud OTP';
  })();

  const pageDescription = description || 'Professional OTP management with zero-knowledge encryption. Secure, reliable, and built for teams.';

  return (
    <div className="min-h-screen bg-white">
      {useSEO && <SEO title={pageTitle} description={pageDescription} />}
      <Navigation />
      <main>
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default PublicLayout;
