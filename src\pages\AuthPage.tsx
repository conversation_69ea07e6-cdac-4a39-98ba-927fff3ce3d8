import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Turnstile } from '@marsidev/react-turnstile'
import { Shield, Mail, Lock, User, Github, Twitter, Facebook, Loader2 } from 'lucide-react';
import { FcGoogle } from 'react-icons/fc';
import { useAuth } from '../hooks/useAuth';
import SEO from '../components/SEO';

const AuthPage: React.FC = () => {
  const { t } = useTranslation(['auth', 'common']);
  const { signIn, signUp, signInWithProvider } = useAuth();
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [captchaToken, setCaptchaToken] = useState();


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      if (isSignUp) {
        await signUp(email, password, firstName);
      } else {
        await signIn(email, password);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const socialProviders = [
    { name: 'github', icon: Github, label: 'GitHub' },
    { name: 'google', icon: FcGoogle, label: 'Google' },
    { name: 'twitter', icon: Twitter, label: 'Twitter' },
    { name: 'facebook', icon: Facebook, label: 'Facebook' },
  ] as const;

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-900 relative overflow-hidden">
      <SEO title={t(isSignUp ? 'auth:signUp' : 'auth:signIn')} />

      {/* Éléments décoratifs pixelisés */}
      <div className="fixed top-20 right-10 w-32 h-32 opacity-20 z-0">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px] animate-pulse-slow"></div>
      </div>
      <div className="fixed bottom-20 left-10 w-40 h-40 opacity-20 z-0">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:15px_15px] animate-pulse-slow"></div>
      </div>

      <div className="bg-dark-800 p-8 rounded-2xl border border-dark-700 shadow-xl w-full max-w-md relative z-10">
        <div className="flex justify-center mb-8">
          <div className="bg-primary-500 p-3 rounded-xl shadow-neon">
            <Shield className="h-10 w-10 text-dark-900" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-center text-white mb-8 font-mono">
          {t(isSignUp ? 'auth:signUp' : 'auth:signIn')}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          {isSignUp && (
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                {t('auth:firstName')}
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary-400" />
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="pl-10 w-full rounded-lg border border-dark-700 bg-dark-900 text-white px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                />
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              {t('auth:email')}
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary-400" />
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-10 w-full rounded-lg border border-dark-700 bg-dark-900 text-white px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-primary-300 mb-2">
              {t('auth:password')}
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary-400" />
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10 w-full rounded-lg border border-dark-700 bg-dark-900 text-white px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-900/30 text-red-400 p-3 rounded-lg text-sm border border-red-800">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-primary-500 text-dark-900 font-bold rounded-xl px-4 py-3 hover:shadow-neon transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed transition-all duration-200"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <Loader2 className="animate-spin h-5 w-5 mr-2" />
                {t('common:loading')}
              </span>
            ) : (
              t(isSignUp ? 'auth:signUp' : 'auth:signIn')
            )}
          </button>

          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-dark-700" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-dark-800 text-primary-400 font-medium">
                  {t('auth:orContinueWith')}
                </span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-4 gap-3">
              {socialProviders.map((provider) => {
                const Icon = provider.icon;
                return (
                  <button
                    key={provider.name}
                    type="button"
                    onClick={() => signInWithProvider(provider.name)}
                    className="inline-flex justify-center items-center py-3 px-4 border border-dark-700 rounded-xl shadow-xl bg-dark-900/80 backdrop-blur-sm text-sm font-medium text-primary-400 hover:bg-dark-800 hover:border-primary-500 hover:text-primary-300 transition-all duration-200 transform hover:-translate-y-1"
                  >
                    <Icon className="h-5 w-5" />
                  </button>
                );
              })}
            </div>

            <div className="text-center mt-8">
              <button
                type="button"
                onClick={() => setIsSignUp(!isSignUp)}
                className="text-sm font-medium text-primary-400 hover:text-primary-300 underline decoration-2 underline-offset-2 hover:underline-offset-4 transition-all duration-200"
              >
                {isSignUp ? t('auth:alreadyHaveAccount') : t('auth:dontHaveAccount')}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AuthPage;