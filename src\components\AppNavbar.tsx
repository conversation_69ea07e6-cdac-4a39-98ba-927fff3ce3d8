import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import {
  Shield, LogOut, Menu, X, User, Settings,
  List, PlusCircle, Home, ChevronDown
} from 'lucide-react';
import LanguageSelector from './LanguageSelector';
import { useAuth } from '../hooks/useAuth';

const AppNavbar: React.FC = () => {
  const { t } = useTranslation(['common', 'otp', 'profile', 'settings', 'auth']);
  const { user, signOut } = useAuth();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Fermer le menu utilisateur lorsqu'on clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header className="bg-dark-800 border-b border-dark-700 sticky top-0 z-50">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Link to="/app" className="flex items-center space-x-3">
            <div className="bg-primary-500 p-2 rounded-lg shadow-neon">
              <Shield className="h-6 w-6 text-dark-900" />
            </div>
            <span className="text-xl font-bold font-mono text-white">
              {t('common:appName')}
            </span>
          </Link>
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:flex items-center space-x-6">
          <Link
            to="/app"
            className={`flex items-center space-x-1 text-sm font-medium ${
              isActive('/app')
                ? 'text-primary-500'
                : 'text-primary-300 hover:text-primary-400'
            } transition-colors duration-200`}
          >
            <Home className="h-4 w-4" />
            <span>{t('otp:dashboard')}</span>
          </Link>

          <Link
            to="/app/add"
            className={`flex items-center space-x-1 text-sm font-medium ${
              isActive('/app/add')
                ? 'text-primary-500'
                : 'text-primary-300 hover:text-primary-400'
            } transition-colors duration-200`}
          >
            <PlusCircle className="h-4 w-4" />
            <span>{t('otp:add')}</span>
          </Link>
        </div>

        {/* User menu (desktop) */}
        <div className="hidden md:flex items-center space-x-4">
          <LanguageSelector />

          <div className="relative">
            <button
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="flex items-center space-x-1 text-sm font-medium text-primary-300 hover:text-primary-400 transition-colors duration-200 rounded-lg px-3 py-1.5"
            >
              <User className="h-4 w-4" />
              <span>{user?.email?.split('@')[0]}</span>
              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${userMenuOpen ? 'rotate-180' : ''}`} />
            </button>

            {userMenuOpen && (
              <div ref={userMenuRef} className="absolute right-0 mt-2 w-48 bg-dark-800 border border-dark-700 rounded-lg shadow-xl z-50">
                <div className="py-1">
                  <Link
                    to="/app/profile"
                    className="flex items-center px-4 py-2 text-sm text-primary-300 hover:bg-dark-700 hover:text-primary-400"
                    onClick={() => setUserMenuOpen(false)}
                  >
                    <User className="h-4 w-4 mr-2" />
                    {t('profile:title')}
                  </Link>

                  <Link
                    to="/app/settings"
                    className="flex items-center px-4 py-2 text-sm text-primary-300 hover:bg-dark-700 hover:text-primary-400"
                    onClick={() => setUserMenuOpen(false)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {t('settings:title')}
                  </Link>

                  <Link
                    to="/app/admin/changelog"
                    className="flex items-center px-4 py-2 text-sm text-primary-300 hover:bg-dark-700 hover:text-primary-400"
                    onClick={() => setUserMenuOpen(false)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Admin: Changelog
                  </Link>

                  <Link
                    to="/app/admin/content"
                    className="flex items-center px-4 py-2 text-sm text-primary-300 hover:bg-dark-700 hover:text-primary-400"
                    onClick={() => setUserMenuOpen(false)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Admin: Content
                  </Link>

                  <hr className="my-1 border-dark-700" />

                  <button
                    onClick={() => {
                      setUserMenuOpen(false);
                      signOut();
                    }}
                    className="flex items-center w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-dark-700"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    {t('auth:signOut')}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Mobile menu button */}
        <div className="md:hidden">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-md text-primary-500 hover:text-primary-400 focus:outline-none"
          >
            {mobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </nav>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-dark-800 border-t border-dark-700 py-3 px-4 shadow-xl z-40">
          <div className="flex flex-col space-y-3">
            <Link
              to="/app"
              className={`flex items-center space-x-2 py-2 px-3 rounded-lg ${
                isActive('/app')
                  ? 'bg-primary-900/30 text-primary-500 border border-primary-800'
                  : 'text-primary-300 hover:bg-dark-700'
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              <Home className="h-5 w-5" />
              <span>{t('otp:dashboard')}</span>
            </Link>

            <Link
              to="/app/add"
              className={`flex items-center space-x-2 py-2 px-3 rounded-lg ${
                isActive('/app/add')
                  ? 'bg-primary-900/30 text-primary-500 border border-primary-800'
                  : 'text-primary-300 hover:bg-dark-700'
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              <PlusCircle className="h-5 w-5" />
              <span>{t('otp:add')}</span>
            </Link>

            <Link
              to="/app/profile"
              className={`flex items-center space-x-2 py-2 px-3 rounded-lg ${
                isActive('/app/profile')
                  ? 'bg-primary-900/30 text-primary-500 border border-primary-800'
                  : 'text-primary-300 hover:bg-dark-700'
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              <User className="h-5 w-5" />
              <span>{t('profile:title')}</span>
            </Link>

            <Link
              to="/app/settings"
              className={`flex items-center space-x-2 py-2 px-3 rounded-lg ${
                isActive('/app/settings')
                  ? 'bg-primary-900/30 text-primary-500 border border-primary-800'
                  : 'text-primary-300 hover:bg-dark-700'
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              <Settings className="h-5 w-5" />
              <span>{t('settings:title')}</span>
            </Link>

            <hr className="my-2 border-dark-700" />

            <LanguageSelector />

            <button
              onClick={() => {
                setMobileMenuOpen(false);
                signOut();
              }}
              className="flex items-center space-x-2 py-2 px-3 rounded-lg text-red-400 hover:bg-dark-700"
            >
              <LogOut className="h-5 w-5" />
              <span>{t('auth:signOut')}</span>
            </button>
          </div>
        </div>
      )}
    </header>
  );
};

export default AppNavbar;
