import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, Archive, CheckCircle, Clock, FileText } from 'lucide-react';
import { changelogService, ChangelogEntry } from '../../services/changelogService';
import FeatureStatusIndicator from '../../components/public/FeatureStatusIndicator';

const ChangelogAdminPage: React.FC = () => {
  const [entries, setEntries] = useState<ChangelogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEntry, setSelectedEntry] = useState<ChangelogEntry | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    loadEntries();
  }, []);

  const loadEntries = async () => {
    setLoading(true);
    try {
      const data = await changelogService.getAllEntries();
      setEntries(data);
    } catch (error) {
      console.error('Error loading changelog entries:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEntry = () => {
    setSelectedEntry({
      version: '',
      date: new Date().toISOString().split('T')[0],
      title: '',
      description: '',
      changes: [''],
      status: 'draft'
    });
    setIsEditing(true);
    setShowForm(true);
  };

  const handleEditEntry = (entry: ChangelogEntry) => {
    setSelectedEntry(entry);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleSaveEntry = async (entry: ChangelogEntry) => {
    try {
      if (entry.id) {
        await changelogService.updateEntry(entry.id, entry);
      } else {
        await changelogService.createEntry(entry);
      }
      await loadEntries();
      setShowForm(false);
      setSelectedEntry(null);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving entry:', error);
    }
  };

  const handleDeleteEntry = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this changelog entry?')) {
      try {
        await changelogService.deleteEntry(id);
        await loadEntries();
      } catch (error) {
        console.error('Error deleting entry:', error);
      }
    }
  };

  const handlePublishEntry = async (id: string) => {
    try {
      await changelogService.publishEntry(id);
      await loadEntries();
    } catch (error) {
      console.error('Error publishing entry:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'draft':
        return <Clock className="h-4 w-4 text-amber-600" />;
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Changelog Management</h1>
            <p className="mt-2 text-gray-600">Manage product updates and version history</p>
          </div>
          <button
            onClick={handleCreateEntry}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Entry
          </button>
        </div>
      </div>

      {/* Entries List */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Changelog Entries</h2>
        </div>

        <div className="divide-y divide-gray-200">
          {entries.map((entry) => (
            <div key={entry.id} className="px-6 py-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {entry.version} - {entry.title}
                    </h3>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(entry.status)}
                      <span className="text-sm text-gray-600 capitalize">{entry.status}</span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-2">{entry.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>Date: {entry.date}</span>
                    <span>{entry.changes.length} changes</span>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {entry.status === 'draft' && (
                    <button
                      onClick={() => entry.id && handlePublishEntry(entry.id)}
                      className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Publish"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    onClick={() => handleEditEntry(entry)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => entry.id && handleDeleteEntry(entry.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Entry Form Modal */}
      {showForm && selectedEntry && (
        <ChangelogEntryForm
          entry={selectedEntry}
          onSave={handleSaveEntry}
          onCancel={() => {
            setShowForm(false);
            setSelectedEntry(null);
            setIsEditing(false);
          }}
        />
      )}
    </div>
  );
};

interface ChangelogEntryFormProps {
  entry: ChangelogEntry;
  onSave: (entry: ChangelogEntry) => void;
  onCancel: () => void;
}

const ChangelogEntryForm: React.FC<ChangelogEntryFormProps> = ({ entry, onSave, onCancel }) => {
  const [formData, setFormData] = useState<ChangelogEntry>(entry);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const addChange = () => {
    setFormData({
      ...formData,
      changes: [...formData.changes, '']
    });
  };

  const updateChange = (index: number, value: string) => {
    const newChanges = [...formData.changes];
    newChanges[index] = value;
    setFormData({
      ...formData,
      changes: newChanges
    });
  };

  const removeChange = (index: number) => {
    setFormData({
      ...formData,
      changes: formData.changes.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {entry.id ? 'Edit' : 'Create'} Changelog Entry
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Version
              </label>
              <input
                type="text"
                value={formData.version}
                onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="e.g., 1.0.0"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., Major Security Update"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Brief description of this release"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as 'draft' | 'published' | 'archived' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Changes
              </label>
              <button
                type="button"
                onClick={addChange}
                className="text-sm text-primary-600 hover:text-primary-700"
              >
                + Add Change
              </button>
            </div>
            <div className="space-y-2">
              {formData.changes.map((change, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={change}
                    onChange={(e) => updateChange(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Describe the change"
                  />
                  <button
                    type="button"
                    onClick={() => removeChange(index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Save Entry
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChangelogAdminPage;