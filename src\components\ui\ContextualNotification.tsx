import React, { useEffect, useState, useRef } from 'react';
import { 
  CheckCircle, 
  AlertCircle, 
  AlertTriangle, 
  Info, 
  X, 
  Bell,
  Shield,
  Lock,
  Key,
  Users,
  Clock,
  Zap,
  Eye,
  Settings
} from 'lucide-react';
import { designUtils } from '../../lib/designSystem';

export type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'security' | 'system';
export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical';
export type NotificationBehavior = 'persistent' | 'auto-dismiss' | 'user-action' | 'contextual';

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
  icon?: React.ComponentType<any>;
}

export interface UserBehaviorContext {
  currentPage?: string;
  userActivity?: 'active' | 'idle' | 'away';
  lastInteraction?: Date;
  sessionDuration?: number;
  previousNotifications?: string[];
  userPreferences?: {
    frequency?: 'minimal' | 'normal' | 'verbose';
    timing?: 'immediate' | 'batched' | 'smart';
    channels?: ('toast' | 'banner' | 'modal' | 'email')[];
  };
}

export interface ContextualNotificationProps {
  // Core Properties
  id: string;
  type: NotificationType;
  priority: NotificationPriority;
  behavior: NotificationBehavior;
  
  // Content
  title: string;
  message: string;
  icon?: React.ComponentType<any>;
  actions?: NotificationAction[];
  
  // Timing and Behavior
  duration?: number; // milliseconds, 0 for persistent
  delay?: number; // delay before showing
  userContext?: UserBehaviorContext;
  
  // Visual Properties
  variant?: 'toast' | 'banner' | 'inline' | 'floating';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  
  // Interaction Properties
  dismissible?: boolean;
  onDismiss?: () => void;
  onShow?: () => void;
  onAction?: (actionIndex: number) => void;
  
  // Accessibility
  ariaLive?: 'polite' | 'assertive' | 'off';
  screenReaderAnnouncement?: string;
  
  className?: string;
}

const ContextualNotification: React.FC<ContextualNotificationProps> = ({
  id,
  type,
  priority,
  behavior,
  title,
  message,
  icon: CustomIcon,
  actions = [],
  duration = 0,
  delay = 0,
  userContext,
  variant = 'toast',
  size = 'md',
  animated = true,
  dismissible = true,
  onDismiss,
  onShow,
  onAction,
  ariaLive = 'polite',
  screenReaderAnnouncement,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isEntering, setIsEntering] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(duration);
  const [isPaused, setIsPaused] = useState(false);
  const timerRef = useRef<NodeJS.Timeout>();
  const countdownRef = useRef<NodeJS.Timeout>();
  const notificationRef = useRef<HTMLDivElement>(null);

  // Calculate smart timing based on user behavior
  const getSmartTiming = (): number => {
    if (!userContext) return duration;

    const { userActivity, userPreferences, sessionDuration } = userContext;
    let adjustedDuration = duration;

    // Adjust based on user activity
    if (userActivity === 'idle') {
      adjustedDuration *= 1.5; // Show longer when idle
    } else if (userActivity === 'active') {
      adjustedDuration *= 0.8; // Show shorter when active
    }

    // Adjust based on user preferences
    if (userPreferences?.timing === 'batched' && priority !== 'critical') {
      adjustedDuration = 0; // Don't auto-dismiss batched notifications
    }

    // Adjust based on session duration (fatigue factor)
    if (sessionDuration && sessionDuration > 3600000) { // > 1 hour
      adjustedDuration *= 1.2; // Show longer for fatigued users
    }

    return Math.max(adjustedDuration, 2000); // Minimum 2 seconds
  };

  // Show notification with delay and smart timing
  useEffect(() => {
    const showTimer = setTimeout(() => {
      setIsVisible(true);
      setIsEntering(true);
      onShow?.();

      // Announce to screen readers
      if (screenReaderAnnouncement) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', ariaLive);
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = screenReaderAnnouncement;
        document.body.appendChild(announcement);
        
        setTimeout(() => document.body.removeChild(announcement), 1000);
      }

      // Set up auto-dismiss timer
      if (behavior === 'auto-dismiss' && duration > 0) {
        const smartDuration = getSmartTiming();
        setTimeRemaining(smartDuration);
        
        timerRef.current = setTimeout(() => {
          handleDismiss();
        }, smartDuration);

        // Countdown timer for visual feedback
        if (smartDuration > 5000) {
          countdownRef.current = setInterval(() => {
            setTimeRemaining(prev => Math.max(0, prev - 100));
          }, 100);
        }
      }
    }, delay);

    return () => {
      clearTimeout(showTimer);
      if (timerRef.current) clearTimeout(timerRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
    };
  }, [delay, duration, behavior, onShow, screenReaderAnnouncement, ariaLive]);

  // Handle mouse enter/leave for pause functionality
  const handleMouseEnter = () => {
    if (behavior === 'auto-dismiss' && timerRef.current) {
      setIsPaused(true);
      clearTimeout(timerRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
    }
  };

  const handleMouseLeave = () => {
    if (behavior === 'auto-dismiss' && isPaused && timeRemaining > 0) {
      setIsPaused(false);
      
      timerRef.current = setTimeout(() => {
        handleDismiss();
      }, timeRemaining);

      if (timeRemaining > 5000) {
        countdownRef.current = setInterval(() => {
          setTimeRemaining(prev => Math.max(0, prev - 100));
        }, 100);
      }
    }
  };

  // Handle dismiss
  const handleDismiss = () => {
    if (!dismissible) return;

    setIsExiting(true);
    
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, animated ? 300 : 0);
  };

  // Handle action click
  const handleActionClick = (actionIndex: number) => {
    const action = actions[actionIndex];
    action.action();
    onAction?.(actionIndex);
    
    // Auto-dismiss after action unless persistent
    if (behavior !== 'persistent') {
      handleDismiss();
    }
  };

  // Get appropriate icon
  const getIcon = () => {
    if (CustomIcon) return CustomIcon;

    switch (type) {
      case 'success':
        return CheckCircle;
      case 'error':
        return AlertCircle;
      case 'warning':
        return AlertTriangle;
      case 'info':
        return Info;
      case 'security':
        return Shield;
      case 'system':
        return Settings;
      default:
        return Bell;
    }
  };

  // Get colors based on type and priority
  const getColors = () => {
    const baseColors = {
      success: {
        bg: 'bg-green-50 dark:bg-green-900/20',
        border: 'border-green-200 dark:border-green-800',
        text: 'text-green-800 dark:text-green-200',
        icon: 'text-green-600 dark:text-green-400',
        progress: 'bg-green-500',
      },
      error: {
        bg: 'bg-red-50 dark:bg-red-900/20',
        border: 'border-red-200 dark:border-red-800',
        text: 'text-red-800 dark:text-red-200',
        icon: 'text-red-600 dark:text-red-400',
        progress: 'bg-red-500',
      },
      warning: {
        bg: 'bg-amber-50 dark:bg-amber-900/20',
        border: 'border-amber-200 dark:border-amber-800',
        text: 'text-amber-800 dark:text-amber-200',
        icon: 'text-amber-600 dark:text-amber-400',
        progress: 'bg-amber-500',
      },
      info: {
        bg: 'bg-blue-50 dark:bg-blue-900/20',
        border: 'border-blue-200 dark:border-blue-800',
        text: 'text-blue-800 dark:text-blue-200',
        icon: 'text-blue-600 dark:text-blue-400',
        progress: 'bg-blue-500',
      },
      security: {
        bg: 'bg-purple-50 dark:bg-purple-900/20',
        border: 'border-purple-200 dark:border-purple-800',
        text: 'text-purple-800 dark:text-purple-200',
        icon: 'text-purple-600 dark:text-purple-400',
        progress: 'bg-purple-500',
      },
      system: {
        bg: 'bg-gray-50 dark:bg-gray-900/20',
        border: 'border-gray-200 dark:border-gray-800',
        text: 'text-gray-800 dark:text-gray-200',
        icon: 'text-gray-600 dark:text-gray-400',
        progress: 'bg-gray-500',
      },
    };

    return baseColors[type];
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'p-3 text-sm',
      icon: 'w-4 h-4',
      title: 'text-sm font-medium',
      message: 'text-xs',
      action: 'px-2 py-1 text-xs',
    },
    md: {
      container: 'p-4 text-sm',
      icon: 'w-5 h-5',
      title: 'text-base font-semibold',
      message: 'text-sm',
      action: 'px-3 py-1.5 text-sm',
    },
    lg: {
      container: 'p-5 text-base',
      icon: 'w-6 h-6',
      title: 'text-lg font-semibold',
      message: 'text-base',
      action: 'px-4 py-2 text-sm',
    },
  };

  // Variant styles
  const variantStyles = {
    toast: 'rounded-lg shadow-lg max-w-sm',
    banner: 'rounded-none w-full',
    inline: 'rounded-md',
    floating: 'rounded-xl shadow-2xl',
  };

  if (!isVisible) return null;

  const IconComponent = getIcon();
  const colors = getColors();
  const config = sizeConfig[size];
  const progressPercentage = duration > 0 ? ((duration - timeRemaining) / duration) * 100 : 0;

  return (
    <div
      ref={notificationRef}
      className={designUtils.cn(
        'relative border transition-all duration-300 ease-out',
        colors.bg,
        colors.border,
        variantStyles[variant],
        config.container,
        animated && isEntering && !isExiting && 'animate-slide-up',
        animated && isExiting && 'animate-fade-out',
        priority === 'critical' && 'ring-2 ring-red-500 ring-opacity-50',
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role="alert"
      aria-live={ariaLive}
      aria-atomic="true"
    >
      {/* Progress bar for auto-dismiss notifications */}
      {behavior === 'auto-dismiss' && duration > 5000 && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-surface-secondary rounded-t-lg overflow-hidden">
          <div
            className={designUtils.cn('h-full transition-all duration-100 ease-linear', colors.progress)}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      )}

      <div className="flex items-start gap-3">
        {/* Icon */}
        <div className={designUtils.cn('flex-shrink-0 mt-0.5', colors.icon, config.icon)}>
          <IconComponent className="w-full h-full" />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4 className={designUtils.cn(colors.text, config.title)}>
            {title}
          </h4>
          <p className={designUtils.cn('mt-1', colors.text, config.message, 'opacity-90')}>
            {message}
          </p>

          {/* Actions */}
          {actions.length > 0 && (
            <div className="flex gap-2 mt-3">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => handleActionClick(index)}
                  className={designUtils.cn(
                    'inline-flex items-center gap-1 rounded font-medium transition-all',
                    config.action,
                    action.style === 'primary' && 'bg-accent text-white hover:bg-accent-hover',
                    action.style === 'danger' && 'bg-red-600 text-white hover:bg-red-700',
                    (!action.style || action.style === 'secondary') && 'bg-surface-elevated border border-border-secondary hover:bg-surface-secondary'
                  )}
                >
                  {action.icon && <action.icon className="w-3 h-3" />}
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Dismiss button */}
        {dismissible && (
          <button
            onClick={handleDismiss}
            className={designUtils.cn(
              'flex-shrink-0 p-1 rounded hover:bg-surface-secondary transition-colors',
              colors.text,
              'opacity-60 hover:opacity-100'
            )}
            aria-label="Dismiss notification"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Pause indicator */}
      {isPaused && behavior === 'auto-dismiss' && (
        <div className="absolute top-2 right-8 text-xs opacity-60">
          Paused
        </div>
      )}
    </div>
  );
};

export default ContextualNotification;