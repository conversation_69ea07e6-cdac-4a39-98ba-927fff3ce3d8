{"getInTouch": "Contactez-Nous", "name": "Nom", "email": "Email", "message": "Message", "namePlaceholder": "Votre nom", "emailPlaceholder": "Votre adresse email", "messagePlaceholder": "Votre message", "submit": "Envoyer le <PERSON>", "sending": "Envoi en cours...", "messageSent": "Message Envoyé !", "thankYou": "Merci de nous avoir contactés. Nous vous répondrons rapidement.", "errorMessage": "Erreur lors de l'envoi de votre message. Veuillez réessayer.", "hero": {"title": "Contactez-Nous", "subtitle": "Une question ? <PERSON><PERSON><PERSON> d'aide ? Notre équipe est là pour vous aider. Utilisez le formulaire ci-dessous."}, "form": {"title": "Envoyez-Nous un Message", "description": "Remplissez le formulaire ci-dessous, nous vous répondrons dès que possible.", "firstName": "Prénom", "lastName": "Nom", "subject": "Sujet", "selectSubject": "Sélectionnez un sujet", "subjects": {"general": "<PERSON><PERSON><PERSON>", "support": "Support Technique", "enterprise": "Solutions Entreprise", "billing": "Question de Facturation", "other": "<PERSON><PERSON>"}, "privacyConsent": "J'accepte la politique de confidentialité et consens au traitement et au stockage de mes données conformément à cette politique.", "submitting": "Envoi...", "submit": "Envoyer le <PERSON>", "successMessage": "Merci pour votre message ! Nous reviendrons vers vous rapidement.", "message": "Message"}, "info": {"title": "Informations de Contact", "description": "Vous avez une question ? Voici comment nous joindre.", "supportHours": {"title": "Heures de Support", "weekdays": "Lund<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "weekdayHours": "9h00 - 18h00 HEC", "weekends": "Samedi - <PERSON><PERSON><PERSON>", "weekendHours": "Support limité par email uniquement"}, "phone": "Le support est actuellement disponible par email uniquement", "address": {"line1": "SecureOTP", "line2": "Solutions de Sécurité Numérique", "line3": "Paris, France"}, "responseTime": {"title": "Temps de Réponse", "description": "Nous nous efforçons de répondre à toutes les demandes aussi rapidement que possible. Voici nos délais de réponse habituels :", "general": "Demandes générales : Sous 24 heures", "technical": "Support technique : Sous 12 heures", "enterprise": "Solutions entreprise : Sous 8 heures ouvrées"}, "email": "<EMAIL>"}, "faq": {"title": "Questions Fréquentes", "subtitle": "Trouvez rapidement des réponses aux questions courantes sur nos services", "viewAll": "Voir toutes les FAQs", "q1": "Qu'est-ce que SecureOTP ?", "a1": "SecureOTP est une plateforme sécurisée pour gérer les mots de passe à usage unique (OTP) utilisés dans l'authentification à deux facteurs. Elle vous aide à stocker, organiser et accéder à vos codes d'authentification en un seul endroit sécurisé.", "q2": "Comment fonctionne SecureOTP ?", "a2": "SecureOTP génère des mots de passe à usage unique basés sur le temps ou sur compteur selon les normes de l'industrie. Vous pouvez ajouter vos comptes, et SecureOTP générera les codes corrects quand vous en aurez besoin.", "q3": "Mes données sont-elles sécurisées avec SecureOTP ?", "a3": "<PERSON><PERSON>, nous utilisons un chiffrement de bout en bout pour protéger vos données. Vos secrets OTP sont chiffrés sur votre appareil avant d'être stockés, et vous seul pouvez les déchiffrer avec votre clé de chiffrement.", "q4": "Puis-je utiliser SecureOTP sur plusieurs appareils ?", "a4": "<PERSON><PERSON>, SecureOTP est conçu pour fonctionner sur plusieurs appareils. Vos données chiffrées sont synchronisées de manière sécurisée, vous permettant d'accéder à vos codes OTP depuis n'importe quel appareil.", "q5": "Comment commencer avec SecureOTP ?", "a5": "Créez simplement un compte, configurez votre clé de chiffrement et commencez à ajouter vos comptes OTP. Vous pouvez scanner des codes QR ou entrer les secrets manuellement pour commencer."}, "map": {"placeholder": "Notre Emplacement", "description": "Nous sommes basés à Paris, France. Notre équipe travaille à distance à travers l'Europe pour vous fournir le meilleur service.", "button": "Voir sur Google Maps", "alert": "La fonctionnalité de carte sera bientôt disponible. Nous sommes actuellement situés à Paris, France."}}