import React from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Users, Globe, Database, Lock, Eye } from 'lucide-react';
import FeatureStatusIndicator, { FeatureStatus } from './FeatureStatusIndicator';

const CleanFeaturesSection: React.FC = () => {
  const { t } = useTranslation(['public']);

  const features: Array<{
    id: string;
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    status: FeatureStatus;
    benefits: string[];
  }> = [
    {
      id: 'zero-knowledge',
      title: 'Zero-Knowledge Security',
      description: 'Your OTP secrets are encrypted client-side with your own password. We never see your plaintext data.',
      icon: Eye,
      status: 'available',
      benefits: ['Client-side encryption', 'AES-256 security', 'Complete privacy']
    },
    {
      id: 'cloud-access',
      title: 'Cloud Synchronization',
      description: 'Access your OTPs from any device, anywhere. Real-time sync across all your devices.',
      icon: Globe,
      status: 'available',
      benefits: ['Multi-device access', 'Real-time sync', 'Offline support']
    },
    {
      id: 'team-sharing',
      title: 'Secure Team Sharing',
      description: 'Share OTP codes securely with team members while maintaining zero-knowledge encryption.',
      icon: Users,
      status: 'development',
      benefits: ['Role-based access', 'Audit trails', 'Secure collaboration']
    },
    {
      id: 'enterprise-ready',
      title: 'Enterprise Features',
      description: 'Advanced admin controls, compliance reporting, and enterprise-grade security features.',
      icon: Shield,
      status: 'development',
      benefits: ['Admin dashboard', 'Compliance reports', 'SSO integration']
    },
    {
      id: 'api-first',
      title: 'Developer API',
      description: 'Comprehensive REST API with SDKs for seamless integration into your applications.',
      icon: Database,
      status: 'beta',
      benefits: ['REST API', 'Multiple SDKs', 'Webhook support']
    },
    {
      id: 'backup-recovery',
      title: 'Secure Backup',
      description: 'Encrypted backups ensure you never lose access to your OTP codes, even if you lose your device.',
      icon: Lock,
      status: 'available',
      benefits: ['Encrypted backups', 'Easy recovery', 'Multiple formats']
    }
  ];



  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need for Secure Authentication
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From individual use to enterprise deployment, our platform scales with your security needs.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature) => {
            const Icon = feature.icon;
            
            return (
              <div
                key={feature.id}
                className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
              >
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <FeatureStatusIndicator status={feature.status} />
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {feature.description}
                </p>

                {/* Benefits */}
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-primary-500 rounded-full flex-shrink-0" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white border border-gray-200 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Secure Your Authentication?
            </h3>
            <p className="text-gray-600 mb-6">
              Start with our free tier and upgrade as your needs grow.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/auth"
                className="bg-primary-600 hover:bg-primary-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors duration-200 text-center"
              >
                Get Started Free
              </a>
              <a
                href="/pricing"
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold px-6 py-3 rounded-lg transition-colors duration-200 text-center"
              >
                View Pricing
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CleanFeaturesSection;