import React, { useEffect, useRef, useState } from 'react';
import { designUtils } from '../../lib/designSystem';

export type BlurIntensity = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
export type BlurDirection = 'top' | 'bottom' | 'left' | 'right' | 'center' | 'radial';
export type BlurTrigger = 'hover' | 'focus' | 'scroll' | 'intersection' | 'manual';

export interface ProgressiveBlurProps {
  children: React.ReactNode;
  
  // Blur Configuration
  intensity?: BlurIntensity;
  direction?: BlurDirection;
  trigger?: BlurTrigger | BlurTrigger[];
  
  // Animation Properties
  duration?: 'fast' | 'normal' | 'slow';
  easing?: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  
  // Interaction Properties
  blurred?: boolean; // Manual control
  onBlurChange?: (blurred: boolean) => void;
  
  // Scroll-based blur
  scrollThreshold?: number; // 0-1, when to trigger blur based on scroll position
  
  // Intersection observer options
  intersectionThreshold?: number;
  intersectionRootMargin?: string;
  
  // Visual Properties
  overlay?: boolean; // Add subtle overlay when blurred
  overlayColor?: string;
  preserveAspectRatio?: boolean;
  
  // Accessibility
  reduceMotion?: boolean;
  ariaLabel?: string;
  className?: string;
}

const ProgressiveBlur: React.FC<ProgressiveBlurProps> = ({
  children,
  intensity = 'md',
  direction = 'center',
  trigger = 'hover',
  duration = 'normal',
  easing = 'ease-out',
  blurred: manualBlurred,
  onBlurChange,
  scrollThreshold = 0.5,
  intersectionThreshold = 0.1,
  intersectionRootMargin = '0px',
  overlay = false,
  overlayColor = 'rgba(0, 0, 0, 0.1)',
  preserveAspectRatio = true,
  reduceMotion = false,
  ariaLabel,
  className,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isBlurred, setIsBlurred] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isIntersecting, setIsIntersecting] = useState(true);

  const triggers = Array.isArray(trigger) ? trigger : [trigger];

  // Handle manual blur control
  useEffect(() => {
    if (manualBlurred !== undefined) {
      setIsBlurred(manualBlurred);
    }
  }, [manualBlurred]);

  // Scroll-based blur
  useEffect(() => {
    if (!triggers.includes('scroll')) return;

    const handleScroll = () => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const elementTop = rect.top;
      const elementHeight = rect.height;

      // Calculate scroll progress (0 = top of viewport, 1 = bottom of viewport)
      const progress = Math.max(0, Math.min(1, (windowHeight - elementTop) / (windowHeight + elementHeight)));
      setScrollProgress(progress);

      const shouldBlur = progress > scrollThreshold;
      if (shouldBlur !== isBlurred) {
        setIsBlurred(shouldBlur);
        onBlurChange?.(shouldBlur);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial check

    return () => window.removeEventListener('scroll', handleScroll);
  }, [triggers, scrollThreshold, isBlurred, onBlurChange]);

  // Intersection observer for visibility-based blur
  useEffect(() => {
    if (!triggers.includes('intersection') || !containerRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        const shouldBlur = !entry.isIntersecting;
        if (shouldBlur !== isBlurred) {
          setIsBlurred(shouldBlur);
          onBlurChange?.(shouldBlur);
        }
      },
      {
        threshold: intersectionThreshold,
        rootMargin: intersectionRootMargin,
      }
    );

    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, [triggers, intersectionThreshold, intersectionRootMargin, isBlurred, onBlurChange]);

  // Determine if blur should be active
  const shouldBlur = manualBlurred !== undefined 
    ? manualBlurred 
    : (triggers.includes('hover') && isHovered) ||
      (triggers.includes('focus') && isFocused) ||
      (triggers.includes('scroll') && scrollProgress > scrollThreshold) ||
      (triggers.includes('intersection') && !isIntersecting) ||
      (triggers.includes('manual') && isBlurred);

  // Get blur CSS classes and styles
  const getBlurStyles = () => {
    if (!shouldBlur || intensity === 'none') return {};

    const blurValues = {
      xs: '2px',
      sm: '4px',
      md: '8px',
      lg: '12px',
      xl: '16px',
      '2xl': '24px',
      '3xl': '40px',
    };

    const blurValue = blurValues[intensity];
    
    // Base blur filter
    let filter = `blur(${blurValue})`;
    
    // Direction-based gradient masks for progressive blur
    let maskImage = '';
    
    switch (direction) {
      case 'top':
        maskImage = 'linear-gradient(to bottom, transparent 0%, black 30%, black 100%)';
        break;
      case 'bottom':
        maskImage = 'linear-gradient(to top, transparent 0%, black 30%, black 100%)';
        break;
      case 'left':
        maskImage = 'linear-gradient(to right, transparent 0%, black 30%, black 100%)';
        break;
      case 'right':
        maskImage = 'linear-gradient(to left, transparent 0%, black 30%, black 100%)';
        break;
      case 'radial':
        maskImage = 'radial-gradient(circle at center, black 0%, black 40%, transparent 70%)';
        break;
      case 'center':
      default:
        // No mask for center blur
        break;
    }

    return {
      filter,
      WebkitFilter: filter,
      ...(maskImage && {
        maskImage,
        WebkitMaskImage: maskImage,
      }),
    };
  };

  // Get transition classes
  const getTransitionClasses = () => {
    if (reduceMotion) return '';

    const durationClasses = {
      fast: 'duration-150',
      normal: 'duration-300',
      slow: 'duration-500',
    };

    const easingClasses = {
      linear: 'ease-linear',
      ease: 'ease',
      'ease-in': 'ease-in',
      'ease-out': 'ease-out',
      'ease-in-out': 'ease-in-out',
    };

    return `transition-all ${durationClasses[duration]} ${easingClasses[easing]}`;
  };

  // Handle mouse events
  const handleMouseEnter = () => {
    if (triggers.includes('hover')) {
      setIsHovered(true);
      if (manualBlurred === undefined) {
        setIsBlurred(true);
        onBlurChange?.(true);
      }
    }
  };

  const handleMouseLeave = () => {
    if (triggers.includes('hover')) {
      setIsHovered(false);
      if (manualBlurred === undefined) {
        setIsBlurred(false);
        onBlurChange?.(false);
      }
    }
  };

  // Handle focus events
  const handleFocus = () => {
    if (triggers.includes('focus')) {
      setIsFocused(true);
      if (manualBlurred === undefined) {
        setIsBlurred(true);
        onBlurChange?.(true);
      }
    }
  };

  const handleBlur = () => {
    if (triggers.includes('focus')) {
      setIsFocused(false);
      if (manualBlurred === undefined) {
        setIsBlurred(false);
        onBlurChange?.(false);
      }
    }
  };

  return (
    <div
      ref={containerRef}
      className={designUtils.cn(
        'relative overflow-hidden',
        preserveAspectRatio && 'aspect-auto',
        getTransitionClasses(),
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      aria-label={ariaLabel}
      style={{
        ...getBlurStyles(),
      }}
    >
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Overlay */}
      {overlay && shouldBlur && (
        <div
          className={designUtils.cn(
            'absolute inset-0 z-20 pointer-events-none',
            getTransitionClasses()
          )}
          style={{
            backgroundColor: overlayColor,
          }}
        />
      )}

      {/* Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 right-2 z-30 text-xs bg-black/50 text-white px-2 py-1 rounded opacity-50">
          Blur: {shouldBlur ? 'ON' : 'OFF'}
          {triggers.includes('scroll') && ` | Scroll: ${Math.round(scrollProgress * 100)}%`}
          {triggers.includes('intersection') && ` | Visible: ${isIntersecting ? 'YES' : 'NO'}`}
        </div>
      )}
    </div>
  );
};

export default ProgressiveBlur;