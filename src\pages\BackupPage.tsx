import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import PageHeader from '../components/public/PageHeader';
import { useAuth } from '../hooks/useAuth';
import { supabase } from '../lib/supabase';
import { useToast } from '../contexts/ToastContext';
import { useEncryption } from '../contexts/EncryptionContext';
import * as encryptionService from '../services/encryptionService';
import PasswordPromptModal from '../components/ui/PasswordPromptModal';
import Button from '../components/ui/Button';

// Define the structure of the backup file
interface BackupFile {
  version: number;
  createdAt: string;
  encryptionKeys: {
    salt: string;
    encrypted_data_key: string;
    data_key_iv: string;
    recovery_encrypted_data_key: string;
    recovery_iv: string;
  };
  secrets: {
    name: string;
    encrypted_secret: string;
    secret_iv: string;
    type: string;
    issuer: string | null;
  }[];
}

const BackupPage: React.FC = () => {
  const { t } = useTranslation(['settings', 'toast']);
  const { user } = useAuth();
  const { addToast } = useToast();
  const { encryptData, isUnlocked } = useEncryption();

  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [backupData, setBackupData] = useState<BackupFile | null>(null);

  const handleExport = async () => {
    if (!user) {
      addToast(t('toast:export.notAuthenticated'), 'error');
      return;
    }

    setIsExporting(true);

    try {
      const { data: secrets, error: secretsError } = await supabase
        .from('otp_secrets')
        .select('name, encrypted_secret, secret_iv, type, issuer')
        .eq('user_id', user.id);

      if (secretsError) throw secretsError;

      const { data: keys, error: keysError } = await supabase
        .from('user_encryption_keys')
        .select('salt, encrypted_data_key, data_key_iv, recovery_encrypted_data_key, recovery_iv')
        .eq('user_id', user.id)
        .single();

      if (keysError) throw keysError;

      const backupFile: BackupFile = {
        version: 1,
        createdAt: new Date().toISOString(),
        encryptionKeys: keys,
        secrets: secrets || [],
      };

      const blob = new Blob([JSON.stringify(backupFile, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'otp-cloud-backup.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      addToast(t('toast:export.success'), 'success');
    } catch (error) {
      console.error('Export failed:', error);
      addToast(t('toast:export.error'), 'error');
    } finally {
      setIsExporting(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleImportClick = () => {
    if (!selectedFile) {
      addToast(t('toast:import.noFile'), 'error');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') throw new Error('Failed to read file');
        const data = JSON.parse(text) as BackupFile;
        // Basic validation
        if (data.version !== 1 || !data.encryptionKeys || !data.secrets) {
          throw new Error('Invalid backup file format');
        }
        setBackupData(data);
        setIsModalOpen(true);
      } catch (error) {
        addToast(t('toast:import.invalidFile'), 'error');
        console.error('Invalid backup file:', error);
      }
    };
    reader.readAsText(selectedFile);
  };

  const processImport = async (password: string) => {
    if (!backupData || !user || !isUnlocked || !encryptData) {
      addToast(t('toast:import.error'), 'error');
      return;
    }

    setIsImporting(true);

    try {
      // 1. Decrypt the data key from the backup file
      const salt = encryptionService.base64ToArrayBuffer(backupData.encryptionKeys.salt);
      let dataKey: CryptoKey;
      try {
        // Try unlocking with password
        const masterKey = await encryptionService.deriveKey(password, salt);
        const decryptedDataKeyBase64 = await encryptionService.decrypt(
          backupData.encryptionKeys.encrypted_data_key,
          backupData.encryptionKeys.data_key_iv,
          masterKey
        );
        const decryptedDataKeyBytes = encryptionService.base64ToArrayBuffer(decryptedDataKeyBase64);
        dataKey = await window.crypto.subtle.importKey(
          'raw',
          decryptedDataKeyBytes,
          { name: 'AES-GCM', length: 256 },
          true,
          ['encrypt', 'decrypt']
        );
      } catch {
        // If password fails, try with recovery key
        const recoveryKeyObj = await encryptionService.importRecoveryKey(password);
        const decryptedDataKeyBase64 = await encryptionService.decrypt(
          backupData.encryptionKeys.recovery_encrypted_data_key,
          backupData.encryptionKeys.recovery_iv,
          recoveryKeyObj
        );
        const decryptedDataKeyBytes = encryptionService.base64ToArrayBuffer(decryptedDataKeyBase64);
        dataKey = await window.crypto.subtle.importKey(
          'raw',
          decryptedDataKeyBytes,
          { name: 'AES-GCM', length: 256 },
          true,
          ['encrypt', 'decrypt']
        );
      }

      // 2. Decrypt secrets from backup and re-encrypt with current user's key
      const secretsToInsert = [];
      for (const secret of backupData.secrets) {
        const decryptedSecret = await encryptionService.decrypt(secret.encrypted_secret, secret.secret_iv, dataKey);
        const { encryptedData: newEncryptedSecret, iv: newSecretIv } = (await encryptData(decryptedSecret))!;

        secretsToInsert.push({
          user_id: user.id,
          name: secret.name,
          encrypted_secret: newEncryptedSecret,
          secret_iv: newSecretIv,
          type: secret.type,
          issuer: secret.issuer,
        });
      }

      // 3. Insert re-encrypted secrets into the database
      if (secretsToInsert.length > 0) {
        const { error } = await supabase.from('otp_secrets').insert(secretsToInsert);
        if (error) throw error;
      }

      addToast(t('toast:import.success'), 'success');
    } catch (error) {
      console.error('Import failed:', error);
      addToast(t('toast:import.error'), 'error');
    } finally {
      setIsImporting(false);
      setBackupData(null);
      setSelectedFile(null);
    }
  };


  return (
    <div className="space-y-8">
      <PasswordPromptModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onConfirm={processImport}
        title={t('backup.import.passwordPromptTitle')}
        description={t('backup.import.passwordPromptDescription')}
      />

      <PageHeader
        title={t('backup.title')}
        description={t('backup.description')}
      />

      <div className="grid gap-8 md:grid-cols-2">
        {/* Export Section */}
        <div className="p-6 bg-white rounded-lg shadow-md dark:bg-gray-800">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{t('backup.export.title')}</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{t('backup.export.description')}</p>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            loading={isExporting}
          >
            {t('backup.export.button')}
          </Button>
        </div>

        {/* Import Section */}
        <div className="p-6 bg-white rounded-lg shadow-md dark:bg-gray-800">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{t('backup.import.title')}</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{t('backup.import.description')}</p>
          <input
            type="file"
            accept=".json"
            onChange={handleFileChange}
            className="mt-4 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <Button
            onClick={handleImportClick}
            disabled={!selectedFile || isImporting}
            loading={isImporting}
            variant="primary"
            className="mt-4"
          >
            {t('backup.import.button')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BackupPage;
