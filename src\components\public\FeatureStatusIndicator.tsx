import React from 'react';
import { CheckCircle, Clock, Wrench, AlertCircle } from 'lucide-react';

export type FeatureStatus = 'available' | 'development' | 'planned' | 'beta';

interface FeatureStatusIndicatorProps {
  status: FeatureStatus;
  className?: string;
  showText?: boolean;
}

const FeatureStatusIndicator: React.FC<FeatureStatusIndicatorProps> = ({
  status,
  className = '',
  showText = true
}) => {
  const getStatusConfig = (status: FeatureStatus) => {
    switch (status) {
      case 'available':
        return {
          icon: CheckCircle,
          text: 'Available Now',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          iconColor: 'text-green-600'
        };
      case 'beta':
        return {
          icon: AlertCircle,
          text: 'Beta',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600'
        };
      case 'development':
        return {
          icon: Wrench,
          text: 'In Development',
          bgColor: 'bg-amber-100',
          textColor: 'text-amber-800',
          iconColor: 'text-amber-600'
        };
      case 'planned':
        return {
          icon: Clock,
          text: 'Planned',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          iconColor: 'text-gray-600'
        };
      default:
        return {
          icon: Clock,
          text: 'Unknown',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          iconColor: 'text-gray-600'
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.bgColor} ${config.textColor} ${className}`}>
      <Icon className={`h-4 w-4 ${config.iconColor} ${showText ? 'mr-2' : ''}`} />
      {showText && config.text}
    </div>
  );
};

export default FeatureStatusIndicator;