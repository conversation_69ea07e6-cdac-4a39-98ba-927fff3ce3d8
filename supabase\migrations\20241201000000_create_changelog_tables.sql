-- Create changelog_categories table
CREATE TABLE IF NOT EXISTS changelog_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create changelog_entries table
CREATE TABLE IF NOT EXISTS changelog_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    version TEXT NOT NULL,
    date DATE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    changes TEXT[] NOT NULL DEFAULT '{}',
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_changelog_entries_status ON changelog_entries(status);
CREATE INDEX IF NOT EXISTS idx_changelog_entries_date ON changelog_entries(date DESC);
CREATE INDEX IF NOT EXISTS idx_changelog_entries_version ON changelog_entries(version);
CREATE INDEX IF NOT EXISTS idx_changelog_categories_order ON changelog_categories("order");

-- Insert default categories
INSERT INTO changelog_categories (name, description, "order") VALUES
    ('New Features', 'New functionality and capabilities', 1),
    ('Improvements', 'Enhancements to existing features', 2),
    ('Security', 'Security updates and fixes', 3),
    ('Bug Fixes', 'Resolved issues and bugs', 4),
    ('API Changes', 'API updates and modifications', 5)
ON CONFLICT DO NOTHING;

-- Insert initial changelog entry
INSERT INTO changelog_entries (version, date, title, description, changes, status) VALUES
    ('0.1.0-alpha.1', '2024-12-01', 'First Alpha Version', 'Initial alpha release of Cloud OTP with core functionality for secure OTP management.', 
     ARRAY[
         'Complete authentication system with Supabase',
         'User account creation with email/password',
         'Zero-knowledge client-side encryption',
         'Add, view, and manage TOTP/HOTP codes',
         'Secure backup and recovery system',
         'Multi-language support (English, French, Spanish)',
         'Responsive design with modern UI',
         'Basic API endpoints for OTP management'
     ], 'published')
ON CONFLICT DO NOTHING;

-- Enable RLS
ALTER TABLE changelog_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE changelog_entries ENABLE ROW LEVEL SECURITY;

-- Create policies for changelog_categories
CREATE POLICY "Anyone can read published changelog categories" ON changelog_categories
    FOR SELECT USING (true);

-- Create policies for changelog_entries
CREATE POLICY "Anyone can read published changelog entries" ON changelog_entries
    FOR SELECT USING (status = 'published');

-- Admin policies (you'll need to create an admin role or use specific user IDs)
-- For now, we'll allow authenticated users to manage changelog entries
-- In production, you should restrict this to admin users only
CREATE POLICY "Authenticated users can manage changelog entries" ON changelog_entries
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage changelog categories" ON changelog_categories
    FOR ALL USING (auth.role() = 'authenticated');

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_changelog_entries_updated_at BEFORE UPDATE ON changelog_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_changelog_categories_updated_at BEFORE UPDATE ON changelog_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();