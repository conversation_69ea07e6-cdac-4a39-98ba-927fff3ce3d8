import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  contentPrioritization, 
  ContentMetrics, 
  PrioritizationContext, 
  PrioritizedContent 
} from '../services/contentPrioritizationService';
import { designUtils } from '../lib/designSystem';

// Type definition for bento card priority
export interface BentoCardPriority {
  level: number;
  weight: number;
  sticky: boolean;
}

export interface UseContentPrioritizationOptions {
  contentId: string;
  metrics: ContentMetrics;
  autoUpdate?: boolean;
  updateInterval?: number;
}

export interface ContentPrioritizationResult {
  priority: BentoCardPriority;
  recommendedSize: {
    width: 'auto' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
    height: 'auto' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  };
  animationDelay: number;
  accessibilityEnhancements: string[];
  updateMetrics: (updates: Partial<ContentMetrics>) => void;
  recalculate: () => void;
}

/**
 * Hook for intelligent content prioritization in bento grid layouts
 */
export const useContentPrioritization = ({
  contentId,
  metrics,
  autoUpdate = true,
  updateInterval = 30000, // 30 seconds
}: UseContentPrioritizationOptions): ContentPrioritizationResult => {
  const [prioritizedContent, setPrioritizedContent] = useState<PrioritizedContent | null>(null);
  const [context, setContext] = useState<PrioritizationContext | null>(null);

  // Create prioritization context from current environment
  const createContext = useCallback((): PrioritizationContext => {
    const screenSize = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    const deviceType = screenSize.width < 768 ? 'mobile' : 
                      screenSize.width < 1024 ? 'tablet' : 'desktop';

    return {
      userType: 'returning', // This could be determined from user data
      deviceType,
      screenSize,
      sessionDuration: performance.now() / 1000,
      currentFocus: document.activeElement?.id || null,
      prefersReducedMotion: designUtils.prefersReducedMotion(),
      prefersHighContrast: designUtils.prefersHighContrast(),
      usesScreenReader: window.navigator.userAgent.includes('NVDA') || 
                       window.navigator.userAgent.includes('JAWS') ||
                       window.speechSynthesis?.speaking === true,
      availableSpace: screenSize,
      gridColumns: screenSize.width < 768 ? 4 : 
                  screenSize.width < 1024 ? 6 : 
                  screenSize.width < 1280 ? 8 : 12,
      maxCards: Math.floor((screenSize.width * screenSize.height) / (280 * 200)),
    };
  }, []);

  // Calculate prioritization
  const calculatePrioritization = useCallback(() => {
    if (!context) return;

    const result = contentPrioritization.calculatePriority(contentId, metrics, context);
    setPrioritizedContent(result);
  }, [contentId, metrics, context]);

  // Initialize context
  useEffect(() => {
    setContext(createContext());
  }, [createContext]);

  // Calculate initial prioritization
  useEffect(() => {
    if (context) {
      calculatePrioritization();
    }
  }, [calculatePrioritization, context]);

  // Auto-update prioritization
  useEffect(() => {
    if (!autoUpdate) return;

    const interval = setInterval(() => {
      setContext(createContext());
      calculatePrioritization();
    }, updateInterval);

    return () => clearInterval(interval);
  }, [autoUpdate, updateInterval, createContext, calculatePrioritization]);

  // Listen for resize events to update context
  useEffect(() => {
    const handleResize = () => {
      setContext(createContext());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [createContext]);

  // Listen for focus changes
  useEffect(() => {
    const handleFocusChange = () => {
      if (context) {
        setContext({
          ...context,
          currentFocus: document.activeElement?.id || null,
        });
      }
    };

    document.addEventListener('focusin', handleFocusChange);
    return () => document.removeEventListener('focusin', handleFocusChange);
  }, [context]);

  // Update metrics function
  const updateMetrics = useCallback((updates: Partial<ContentMetrics>) => {
    contentPrioritization.updateUserBehavior(contentId, updates);
    calculatePrioritization();
  }, [contentId, calculatePrioritization]);

  // Recalculate function
  const recalculate = useCallback(() => {
    setContext(createContext());
    calculatePrioritization();
  }, [createContext, calculatePrioritization]);

  // Convert prioritized content to hook result
  const result = useMemo((): ContentPrioritizationResult => {
    if (!prioritizedContent) {
      // Return default values while calculating
      return {
        priority: { level: 3, weight: 50, sticky: false },
        recommendedSize: { width: 'auto', height: 'auto' },
        animationDelay: 0,
        accessibilityEnhancements: [],
        updateMetrics,
        recalculate,
      };
    }

    // Convert recommended size to component format
    const sizeMap = {
      2: 'sm' as const,
      3: 'md' as const,
      4: 'lg' as const,
      6: 'xl' as const,
    };

    const recommendedSize = {
      width: sizeMap[prioritizedContent.recommendedSize.columns as keyof typeof sizeMap] || 'auto' as const,
      height: prioritizedContent.recommendedSize.rows === 1 ? 'sm' as const :
              prioritizedContent.recommendedSize.rows === 2 ? 'md' as const :
              prioritizedContent.recommendedSize.rows === 3 ? 'lg' as const : 'auto' as const,
    };

    return {
      priority: {
        level: prioritizedContent.priority,
        weight: prioritizedContent.weight,
        sticky: prioritizedContent.sticky,
      },
      recommendedSize,
      animationDelay: prioritizedContent.animationDelay,
      accessibilityEnhancements: prioritizedContent.accessibilityEnhancements,
      updateMetrics,
      recalculate,
    };
  }, [prioritizedContent, updateMetrics, recalculate]);

  return result;
};

/**
 * Hook for tracking user interactions with content
 */
export const useContentInteractionTracking = (contentId: string) => {
  const [startTime] = useState(Date.now());
  const [interactionCount, setInteractionCount] = useState(0);

  const trackView = useCallback(() => {
    contentPrioritization.updateUserBehavior(contentId, {
      viewCount: 1,
      lastAccessed: new Date(),
    });
  }, [contentId]);

  const trackInteraction = useCallback(() => {
    setInteractionCount(prev => prev + 1);
    contentPrioritization.updateUserBehavior(contentId, {
      interactionCount: interactionCount + 1,
      lastAccessed: new Date(),
    });
  }, [contentId, interactionCount]);

  const trackTimeSpent = useCallback(() => {
    const timeSpent = (Date.now() - startTime) / 1000;
    contentPrioritization.updateUserBehavior(contentId, {
      timeSpent,
      lastAccessed: new Date(),
    });
  }, [contentId, startTime]);

  // Track view on mount
  useEffect(() => {
    trackView();
  }, [trackView]);

  // Track time spent on unmount
  useEffect(() => {
    return () => {
      trackTimeSpent();
    };
  }, [trackTimeSpent]);

  return {
    trackInteraction,
    trackTimeSpent,
    interactionCount,
  };
};

/**
 * Hook for managing layout transitions
 */
export const useLayoutTransitions = (enabled = true) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [transitionDuration, setTransitionDuration] = useState(300);

  const startTransition = useCallback((duration = 300) => {
    if (!enabled || designUtils.prefersReducedMotion()) return;

    setIsTransitioning(true);
    setTransitionDuration(duration);

    setTimeout(() => {
      setIsTransitioning(false);
    }, duration);
  }, [enabled]);

  const getTransitionStyles = useCallback(() => {
    if (!enabled || designUtils.prefersReducedMotion()) {
      return {};
    }

    return {
      transition: `all ${transitionDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
      willChange: isTransitioning ? 'transform, opacity' : 'auto',
    };
  }, [enabled, transitionDuration, isTransitioning]);

  return {
    isTransitioning,
    startTransition,
    getTransitionStyles,
  };
};