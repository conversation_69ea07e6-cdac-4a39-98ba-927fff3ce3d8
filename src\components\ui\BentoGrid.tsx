import React, { useEffect, useRef, useState, useCallback } from 'react';
import { designUtils } from '../../lib/designSystem';

export interface BentoGridProps {
  children: React.ReactNode;
  className?: string;
  
  // Grid Configuration
  columns?: 'auto' | 4 | 6 | 8 | 12;
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  minCardWidth?: string;
  minCardHeight?: string;
  
  // Layout Behavior
  variant?: 'standard' | 'advanced' | 'masonry';
  autoFlow?: 'row' | 'column' | 'dense';
  
  // Responsive Behavior
  responsive?: boolean;
  
  // Animation
  animateChildren?: boolean;
  staggerDelay?: number;
  
  // Container Queries
  containerQueries?: boolean;
}

export interface BentoGridContextValue {
  registerCard: (id: string, priority: number) => void;
  unregisterCard: (id: string) => void;
  updateLayout: () => void;
}

export const BentoGridContext = React.createContext<BentoGridContextValue | null>(null);

export const BentoGrid: React.FC<BentoGridProps> = ({
  children,
  className,
  columns = 'auto',
  gap = 'md',
  minCardWidth = '280px',
  minCardHeight = '200px',
  variant = 'standard',
  autoFlow = 'row',
  responsive = true,
  animateChildren = true,
  staggerDelay = 100,
  containerQueries = true,
}) => {
  const gridRef = useRef<HTMLDivElement>(null);
  const [registeredCards, setRegisteredCards] = useState<Map<string, number>>(new Map());
  const [mounted, setMounted] = useState(false);

  // Register/unregister cards for priority-based positioning
  const registerCard = useCallback((id: string, priority: number) => {
    setRegisteredCards(prev => new Map(prev).set(id, priority));
  }, []);

  const unregisterCard = useCallback((id: string) => {
    setRegisteredCards(prev => {
      const newMap = new Map(prev);
      newMap.delete(id);
      return newMap;
    });
  }, []);

  // Update layout based on registered cards
  const updateLayout = useCallback(() => {
    if (!gridRef.current) return;

    // Sort cards by priority and update their order
    const sortedCards = Array.from(registeredCards.entries())
      .sort(([, a], [, b]) => a - b);

    sortedCards.forEach(([id], index) => {
      const cardElement = gridRef.current?.querySelector(`[data-bento-id="${id}"]`) as HTMLElement;
      if (cardElement) {
        cardElement.style.order = index.toString();
      }
    });
  }, [registeredCards]);

  // Context value
  const contextValue: BentoGridContextValue = {
    registerCard,
    unregisterCard,
    updateLayout,
  };

  // Update layout when cards change
  useEffect(() => {
    updateLayout();
  }, [updateLayout]);

  // Mount animation
  useEffect(() => {
    setMounted(true);
  }, []);

  // Stagger animation for children
  useEffect(() => {
    if (!animateChildren || !mounted || !gridRef.current) return;

    const cards = gridRef.current.querySelectorAll('.bento-card');
    cards.forEach((card, index) => {
      const element = card as HTMLElement;
      element.style.animationDelay = `${index * staggerDelay}ms`;
    });
  }, [animateChildren, mounted, staggerDelay, children]);

  // Build grid classes
  const gridClasses = designUtils.cn(
    // Base grid class
    {
      'bento-grid': variant === 'standard',
      'bento-grid-advanced': variant === 'advanced',
      'bento-grid-masonry': variant === 'masonry',
    },
    
    // Gap classes
    {
      'gap-1': gap === 'xs',
      'gap-2': gap === 'sm',
      'gap-4': gap === 'md',
      'gap-6': gap === 'lg',
      'gap-8': gap === 'xl',
    },
    
    // Auto flow
    {
      'grid-flow-row': autoFlow === 'row',
      'grid-flow-col': autoFlow === 'column',
      'grid-flow-row-dense': autoFlow === 'dense',
    },
    
    // Container queries
    {
      'container-responsive': containerQueries,
    },
    
    // Animation
    {
      'animate-fade-in': animateChildren && mounted,
    },
    
    className
  );

  // Dynamic grid styles
  const gridStyles: React.CSSProperties = {
    // Column configuration
    ...(variant === 'standard' && columns !== 'auto' && {
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
    }),
    ...(variant === 'standard' && columns === 'auto' && {
      gridTemplateColumns: `repeat(auto-fit, minmax(${minCardWidth}, 1fr))`,
    }),
    ...(variant === 'advanced' && {
      gridTemplateColumns: `repeat(${columns === 'auto' ? 12 : columns}, 1fr)`,
    }),
    
    // Row configuration
    gridAutoRows: `minmax(${minCardHeight}, auto)`,
  };

  return (
    <BentoGridContext.Provider value={contextValue}>
      <div
        ref={gridRef}
        className={gridClasses}
        style={gridStyles}
        role="grid"
        aria-label="Bento grid layout"
      >
        {children}
      </div>
    </BentoGridContext.Provider>
  );
};

// Hook to use BentoGrid context
export const useBentoGrid = () => {
  const context = React.useContext(BentoGridContext);
  if (!context) {
    throw new Error('useBentoGrid must be used within a BentoGrid component');
  }
  return context;
};

// Masonry grid variant (for future implementation)
const MasonryGrid: React.FC<BentoGridProps> = ({ children, className, gap = 'md' }) => {
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Masonry layout implementation would go here
    // This is a placeholder for future masonry functionality
  }, [children]);

  return (
    <div
      ref={gridRef}
      className={designUtils.cn(
        'columns-1 sm:columns-2 md:columns-3 lg:columns-4',
        gap === 'xs' && 'gap-1',
        gap === 'sm' && 'gap-2',
        gap === 'md' && 'gap-4',
        gap === 'lg' && 'gap-6',
        gap === 'xl' && 'gap-8',
        className
      )}
    >
      {children}
    </div>
  );
};

export default BentoGrid;