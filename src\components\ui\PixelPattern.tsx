import React from 'react';

interface PixelPatternProps {
  className?: string;
  color?: string;
  size?: number;
  density?: number;
  animated?: boolean;
}

const PixelPattern: React.FC<PixelPatternProps> = ({
  className = '',
  color = 'secondary',
  size = 20,
  density = 0.3,
  animated = false,
}) => {
  // Générer une grille de pixels avec une densité variable
  const pixels = [];
  const rows = Math.ceil(100 / size);
  const cols = Math.ceil(100 / size);
  
  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      // Ajouter un pixel avec une probabilité basée sur la densité
      if (Math.random() < density) {
        pixels.push(
          <div
            key={`${i}-${j}`}
            className={`absolute ${
              animated ? 'animate-pixel-shift' : ''
            } bg-${color}-${Math.random() > 0.5 ? '500' : '400'}`}
            style={{
              width: `${size}px`,
              height: `${size}px`,
              left: `${j * size}px`,
              top: `${i * size}px`,
              animationDelay: `${Math.random() * 2}s`,
            }}
          />
        );
      }
    }
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {pixels}
    </div>
  );
};

export default PixelPattern;
