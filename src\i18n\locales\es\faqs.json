{"question1": "¿Qué es SecureOTP?", "answer1": "SecureOTP es una plataforma segura para generar, gestionar y compartir contraseñas de un solo uso (OTP) para la autenticación de dos factores. Ofrece una plataforma web, extensiones para navegadores y aplicaciones móviles/de escritorio.", "question2": "¿Es SecureOTP gratuito?", "answer2": "Sí, SecureOTP es actualmente gratuito durante su fase beta. En el futuro, planeamos ofrecer niveles gratuitos y premium con características adicionales.", "question3": "¿Qué tan segura está mi información con SecureOTP?", "answer3": "Utilizamos cifrado de grado militar para proteger tus secretos OTP. Tus datos están cifrados tanto en reposo como en tránsito, y empleamos un sistema de doble cifrado con una clave de descifrado secundaria opcional para máxima seguridad.", "question4": "¿Puedo usar SecureOTP sin conexión?", "answer4": "Sí, nuestras aplicaciones móviles y de escritorio pueden generar códigos OTP incluso sin conexión a internet, asegurando que siempre tengas acceso a tus códigos de autenticación.", "question5": "¿Cómo contacto al soporte?", "answer5": "Puedes contactar a nuestro equipo de soporte por correo electró<NAME_EMAIL>. Nos esforzamos por responder a todas las consultas dentro de las 24 horas.", "q1": "¿Qué es SecureOTP?", "a1": "SecureOTP es una plataforma segura para gestionar contraseñas de un solo uso (OTP) utilizadas en la autenticación de dos factores. Te ayuda a almacenar, organizar y acceder a tus códigos de autenticación en un lugar seguro.", "q2": "¿Cómo funciona SecureOTP?", "a2": "SecureOTP almacena de forma segura las claves secretas utilizadas para generar códigos OTP. Cuando necesitas un código, la aplicación lo genera automáticamente basándose en el algoritmo correcto (TOTP o HOTP) y te lo muestra para que puedas usarlo para iniciar sesión en tus servicios.", "q3": "¿Qué es la autenticación de dos factores (2FA)?", "a3": "La autenticación de dos factores (2FA) es una capa adicional de seguridad que requiere no solo una contraseña, sino también un segundo factor (como un código OTP) para verificar tu identidad. Esto hace que sea mucho más difícil para los atacantes acceder a tus cuentas, incluso si conocen tu contraseña.", "q4": "¿Cuál es la diferencia entre TOTP y HOTP?", "a4": "TOTP (Time-based One-Time Password) genera códigos basados en la hora actual y cambia automáticamente cada 30 segundos. HOTP (HMAC-based One-Time Password) genera códigos basados en un contador que avanza cada vez que se genera un nuevo código. TOTP es más común para la mayoría de los servicios web.", "q5": "¿Cómo añado un nuevo código OTP?", "a5": "Puedes añadir un nuevo código OTP escaneando un código QR con nuestra aplicación, o introduciendo manualmente los detalles como la clave secreta, el emisor y el tipo de OTP (TOTP o HOTP).", "q6": "¿Qué pasa si pierdo mi dispositivo?", "a6": "Si has configurado la sincronización en la nube, puedes acceder a tus códigos OTP desde cualquier otro dispositivo iniciando sesión en tu cuenta. También recomendamos guardar los códigos de recuperación proporcionados por los servicios que utilizas 2FA, ya que te permitirán acceder a tus cuentas mientras configuras un nuevo dispositivo.", "q7": "¿Es seguro almacenar todos mis códigos OTP en un solo lugar?", "a7": "SecureOTP utiliza cifrado de extremo a extremo para proteger tus datos. Tus claves secretas están cifradas con una clave que solo tú conoces, lo que significa que incluso si nuestros servidores fueran comprometidos, tus datos seguirían siendo inaccesibles sin tu clave de cifrado personal.", "q8": "¿Puedo compartir códigos OTP con mi equipo?", "a8": "Sí, los planes de equipo (próximamente) permitirán compartir códigos OTP de forma segura con miembros del equipo, con controles granulares sobre quién puede ver y usar cada código.", "q9": "¿Qué pasa si olvido mi contraseña?", "a9": "<PERSON>uedes restablecer tu contraseña utilizando el enlace 'Olvidé mi contraseña' en la página de inicio de sesión. Sin embargo, si has habilitado el cifrado de doble capa y pierdes tu clave de cifrado secundaria, no podremos recuperar tus datos cifrados.", "q10": "¿SecureOTP funciona con todos los servicios que usan 2FA?", "a10": "SecureOTP es compatible con la mayoría de los servicios que utilizan algoritmos estándar TOTP o HOTP para la autenticación de dos factores, incluyendo Google, Microsoft, Amazon, Facebook, Twitter, y muchos más."}