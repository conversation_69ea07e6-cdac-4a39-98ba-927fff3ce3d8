// Bento Grid System Exports
export { BentoGrid, useBentoGrid } from './BentoGrid';
export type {
  BentoGridProps,
  BentoGridContextValue,
} from './BentoGrid';

export { IntelligentBentoDemo } from './IntelligentBentoDemo';

// Security-focused components
export { default as SecurityBadge } from './SecurityBadge';
export type { 
  EncryptionStatus, 
  SecurityLevel, 
  BadgeVariant, 
  SecurityBadgeProps 
} from './SecurityBadge';

export { default as ProgressiveBlur } from './ProgressiveBlur';
export type { 
  BlurIntensity, 
  BlurDirection, 
  BlurTrigger, 
  ProgressiveBlurProps 
} from './ProgressiveBlur';

// Intelligent notification system
export { default as ContextualNotification } from './ContextualNotification';
export type {
  NotificationType,
  NotificationPriority,
  NotificationBehavior,
  NotificationAction,
  UserBehaviorContext,
  ContextualNotificationProps
} from './ContextualNotification';

export { default as NotificationCenter } from './NotificationCenter';
export type {
  NotificationQueueItem,
  NotificationCenterProps
} from './NotificationCenter';

export { default as ToastNotification, ToastContainer, ToastProvider, useToast } from './ToastNotification';
export type {
  ToastType,
  ToastPosition,
  ToastNotificationProps,
  ToastContainerProps
} from './ToastNotification';

// Re-export existing components
export { default as Button } from './Button';
export { default as Input } from './Input';
export { default as Modal } from './Modal';
export { default as OTPCard } from './OTPCard';
export { default as OTPCardSimple } from './OTPCardSimple';