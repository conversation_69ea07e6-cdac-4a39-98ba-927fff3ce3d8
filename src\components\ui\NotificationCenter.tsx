import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Bell, <PERSON>, <PERSON>ting<PERSON>, Filter, Archive, Trash2, CheckCircle } from 'lucide-react';
import { designUtils } from '../../lib/designSystem';
import ContextualNotification, { 
  ContextualNotificationProps, 
  NotificationPriority, 
  NotificationType,
  UserBehaviorContext 
} from './ContextualNotification';

export interface NotificationQueueItem extends Omit<ContextualNotificationProps, 'onDismiss' | 'onShow'> {
  timestamp: Date;
  read?: boolean;
  archived?: boolean;
  category?: string;
  source?: string;
}

export interface NotificationCenterProps {
  // Core Properties
  notifications: NotificationQueueItem[];
  maxVisible?: number;
  maxQueue?: number;
  
  // Positioning
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  offset?: { x: number; y: number };
  
  // Behavior
  smartPositioning?: boolean;
  priorityQueuing?: boolean;
  batchSimilar?: boolean;
  respectUserContext?: boolean;
  
  // User Context
  userContext?: UserBehaviorContext;
  
  // Visual Properties
  showCenter?: boolean;
  centerTitle?: string;
  animated?: boolean;
  
  // Interaction
  onNotificationDismiss?: (id: string) => void;
  onNotificationAction?: (id: string, actionIndex: number) => void;
  onCenterToggle?: (open: boolean) => void;
  onSettingsClick?: () => void;
  
  // Accessibility
  announceNewNotifications?: boolean;
  
  className?: string;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  notifications,
  maxVisible = 5,
  maxQueue = 50,
  position = 'top-right',
  offset = { x: 16, y: 16 },
  smartPositioning = true,
  priorityQueuing = true,
  batchSimilar = true,
  respectUserContext = true,
  userContext,
  showCenter = true,
  centerTitle = 'Notifications',
  animated = true,
  onNotificationDismiss,
  onNotificationAction,
  onCenterToggle,
  onSettingsClick,
  announceNewNotifications = true,
  className,
}) => {
  const [visibleNotifications, setVisibleNotifications] = useState<NotificationQueueItem[]>([]);
  const [queuedNotifications, setQueuedNotifications] = useState<NotificationQueueItem[]>([]);
  const [centerOpen, setCenterOpen] = useState(false);
  const [filterType, setFilterType] = useState<NotificationType | 'all'>('all');
  const [unreadCount, setUnreadCount] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const previousNotificationsRef = useRef<NotificationQueueItem[]>([]);

  // Priority order for queuing
  const priorityOrder: Record<NotificationPriority, number> = {
    critical: 0,
    high: 1,
    medium: 2,
    low: 3,
  };

  // Smart positioning based on viewport and user context
  const getSmartPosition = useCallback(() => {
    if (!smartPositioning || !containerRef.current) return position;

    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    const container = containerRef.current.getBoundingClientRect();

    // Avoid positioning over important UI elements
    if (userContext?.currentPage === 'dashboard' && position.includes('top')) {
      return position.replace('top', 'bottom') as typeof position;
    }

    // Adjust for small screens
    if (viewport.width < 768) {
      return 'top-center';
    }

    return position;
  }, [position, smartPositioning, userContext]);

  // Process and queue notifications
  useEffect(() => {
    const processNotifications = () => {
      let processed = [...notifications];

      // Filter out archived notifications from visible queue
      processed = processed.filter(n => !n.archived);

      // Batch similar notifications if enabled
      if (batchSimilar) {
        const batched: NotificationQueueItem[] = [];
        const groups = new Map<string, NotificationQueueItem[]>();

        processed.forEach(notification => {
          const key = `${notification.type}-${notification.title}`;
          if (!groups.has(key)) {
            groups.set(key, []);
          }
          groups.get(key)!.push(notification);
        });

        groups.forEach(group => {
          if (group.length > 1) {
            // Create a batched notification
            const first = group[0];
            batched.push({
              ...first,
              id: `batch-${first.id}`,
              title: `${first.title} (${group.length})`,
              message: `${group.length} similar notifications`,
              actions: [
                {
                  label: 'View All',
                  action: () => setCenterOpen(true),
                },
                ...(first.actions || []),
              ],
            });
          } else {
            batched.push(group[0]);
          }
        });

        processed = batched;
      }

      // Sort by priority and timestamp
      if (priorityQueuing) {
        processed.sort((a, b) => {
          const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
          if (priorityDiff !== 0) return priorityDiff;
          return b.timestamp.getTime() - a.timestamp.getTime();
        });
      }

      // Respect user context for timing
      if (respectUserContext && userContext) {
        const { userActivity, userPreferences } = userContext;
        
        // Delay non-critical notifications if user is active
        if (userActivity === 'active') {
          processed = processed.filter(n => 
            n.priority === 'critical' || 
            n.priority === 'high' ||
            n.behavior === 'persistent'
          );
        }

        // Batch notifications if user prefers it
        if (userPreferences?.timing === 'batched') {
          const critical = processed.filter(n => n.priority === 'critical');
          const others = processed.filter(n => n.priority !== 'critical');
          
          if (others.length > 0) {
            // Show critical immediately, batch others
            processed = critical;
            // Others would be shown in notification center
          }
        }
      }

      // Split into visible and queued
      const visible = processed.slice(0, maxVisible);
      const queued = processed.slice(maxVisible, maxQueue);

      setVisibleNotifications(visible);
      setQueuedNotifications(queued);
    };

    processNotifications();
  }, [notifications, maxVisible, maxQueue, priorityQueuing, batchSimilar, respectUserContext, userContext]);

  // Track unread count
  useEffect(() => {
    const unread = notifications.filter(n => !n.read && !n.archived).length;
    setUnreadCount(unread);
  }, [notifications]);

  // Announce new notifications to screen readers
  useEffect(() => {
    if (!announceNewNotifications) return;

    const newNotifications = notifications.filter(
      n => !previousNotificationsRef.current.some(prev => prev.id === n.id)
    );

    if (newNotifications.length > 0) {
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.setAttribute('aria-atomic', 'true');
      announcement.className = 'sr-only';
      
      if (newNotifications.length === 1) {
        const notification = newNotifications[0];
        announcement.textContent = `New ${notification.type} notification: ${notification.title}`;
      } else {
        announcement.textContent = `${newNotifications.length} new notifications`;
      }
      
      document.body.appendChild(announcement);
      setTimeout(() => document.body.removeChild(announcement), 1000);
    }

    previousNotificationsRef.current = [...notifications];
  }, [notifications, announceNewNotifications]);

  // Handle notification dismiss
  const handleDismiss = (id: string) => {
    setVisibleNotifications(prev => prev.filter(n => n.id !== id));
    onNotificationDismiss?.(id);
  };

  // Handle notification action
  const handleAction = (id: string, actionIndex: number) => {
    onNotificationAction?.(id, actionIndex);
  };

  // Handle center toggle
  const handleCenterToggle = () => {
    const newState = !centerOpen;
    setCenterOpen(newState);
    onCenterToggle?.(newState);
  };

  // Get position styles
  const getPositionStyles = () => {
    const smartPos = getSmartPosition();
    const styles: React.CSSProperties = {
      position: 'fixed',
      zIndex: 1080, // Above modals
    };

    switch (smartPos) {
      case 'top-right':
        styles.top = offset.y;
        styles.right = offset.x;
        break;
      case 'top-left':
        styles.top = offset.y;
        styles.left = offset.x;
        break;
      case 'bottom-right':
        styles.bottom = offset.y;
        styles.right = offset.x;
        break;
      case 'bottom-left':
        styles.bottom = offset.y;
        styles.left = offset.x;
        break;
      case 'top-center':
        styles.top = offset.y;
        styles.left = '50%';
        styles.transform = 'translateX(-50%)';
        break;
      case 'bottom-center':
        styles.bottom = offset.y;
        styles.left = '50%';
        styles.transform = 'translateX(-50%)';
        break;
    }

    return styles;
  };

  // Filter notifications for center
  const filteredNotifications = notifications.filter(n => 
    filterType === 'all' || n.type === filterType
  );

  return (
    <>
      {/* Notification Stack */}
      <div
        ref={containerRef}
        className={designUtils.cn('pointer-events-none', className)}
        style={getPositionStyles()}
      >
        <div className="space-y-2 pointer-events-auto">
          {visibleNotifications.map((notification, index) => (
            <div
              key={notification.id}
              className={designUtils.cn(
                'transition-all duration-300',
                animated && 'animate-slide-up'
              )}
              style={{
                animationDelay: animated ? `${index * 100}ms` : '0ms',
              }}
            >
              <ContextualNotification
                {...notification}
                onDismiss={() => handleDismiss(notification.id)}
                onAction={(actionIndex) => handleAction(notification.id, actionIndex)}
                userContext={userContext}
              />
            </div>
          ))}
        </div>

        {/* Queue indicator */}
        {queuedNotifications.length > 0 && (
          <div className="mt-2 pointer-events-auto">
            <button
              onClick={handleCenterToggle}
              className="w-full px-3 py-2 text-sm bg-surface-elevated border border-border-secondary rounded-lg hover:bg-surface-secondary transition-colors text-center"
            >
              +{queuedNotifications.length} more notifications
            </button>
          </div>
        )}
      </div>

      {/* Notification Center Button */}
      {showCenter && (
        <button
          onClick={handleCenterToggle}
          className={designUtils.cn(
            'fixed bottom-4 right-4 p-3 bg-accent text-white rounded-full shadow-lg hover:shadow-xl transition-all z-50',
            unreadCount > 0 && 'animate-pulse'
          )}
          aria-label={`Open notification center (${unreadCount} unread)`}
        >
          <Bell className="w-5 h-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </button>
      )}

      {/* Notification Center Modal */}
      {centerOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
          <div className="bg-surface-elevated rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border-secondary">
              <h2 className="text-lg font-semibold">{centerTitle}</h2>
              <div className="flex items-center gap-2">
                {/* Filter */}
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as typeof filterType)}
                  className="px-2 py-1 text-sm border border-border-secondary rounded bg-surface-primary"
                >
                  <option value="all">All</option>
                  <option value="security">Security</option>
                  <option value="system">System</option>
                  <option value="info">Info</option>
                  <option value="warning">Warning</option>
                  <option value="error">Error</option>
                  <option value="success">Success</option>
                </select>

                {/* Settings */}
                {onSettingsClick && (
                  <button
                    onClick={onSettingsClick}
                    className="p-1 hover:bg-surface-secondary rounded"
                    aria-label="Notification settings"
                  >
                    <Settings className="w-4 h-4" />
                  </button>
                )}

                {/* Close */}
                <button
                  onClick={handleCenterToggle}
                  className="p-1 hover:bg-surface-secondary rounded"
                  aria-label="Close notification center"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {filteredNotifications.length === 0 ? (
                <div className="text-center py-8 text-text-tertiary">
                  <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No notifications</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={designUtils.cn(
                        'p-3 rounded-lg border transition-all',
                        notification.read 
                          ? 'bg-surface-secondary border-border-secondary opacity-75' 
                          : 'bg-surface-elevated border-border-primary'
                      )}
                    >
                      <ContextualNotification
                        {...notification}
                        variant="inline"
                        behavior="persistent"
                        dismissible={false}
                        animated={false}
                      />
                      
                      {/* Actions */}
                      <div className="flex items-center justify-between mt-2 pt-2 border-t border-border-secondary">
                        <span className="text-xs text-text-tertiary">
                          {notification.timestamp.toLocaleString()}
                        </span>
                        <div className="flex gap-1">
                          {!notification.read && (
                            <button
                              onClick={() => {/* Mark as read */}}
                              className="p-1 text-xs hover:bg-surface-secondary rounded"
                              title="Mark as read"
                            >
                              <CheckCircle className="w-3 h-3" />
                            </button>
                          )}
                          <button
                            onClick={() => {/* Archive */}}
                            className="p-1 text-xs hover:bg-surface-secondary rounded"
                            title="Archive"
                          >
                            <Archive className="w-3 h-3" />
                          </button>
                          <button
                            onClick={() => handleDismiss(notification.id)}
                            className="p-1 text-xs hover:bg-surface-secondary rounded text-red-600"
                            title="Delete"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NotificationCenter;