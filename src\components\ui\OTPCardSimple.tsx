import React from 'react';
import { Shield } from 'lucide-react';
import PixelPattern from './PixelPattern';

interface OTPCardSimpleProps {
  name: string;
  issuer?: string | null;
  className?: string;
  onClick?: () => void;
}

const OTPCardSimple: React.FC<OTPCardSimpleProps> = ({
  name,
  issuer,
  className = '',
  onClick,
}) => {
  return (
    <div
      onClick={onClick}
      className={`relative w-full h-48 bg-dark-800 rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-500 cursor-pointer group hover:shadow-neon-purple ${className}`}
    >
      {/* Motif de pixels en arrière-plan */}
      <div className="absolute inset-0 opacity-30">
        <PixelPattern color="secondary" size={10} density={0.2} animated={true} />
      </div>

      {/* Grille de points */}
      <div className="absolute inset-0 bg-grid-pattern bg-[length:20px_20px]"></div>

      {/* Contenu de la carte */}
      <div className="absolute inset-0 p-4 flex flex-col justify-between z-1">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-primary-500 flex items-center justify-center shadow-neon">
            <Shield className="h-5 w-5 text-dark-900" />
          </div>
          <span className="ml-2 text-white font-bold font-mono">SecureOTP</span>
        </div>

        {/* Icône centrale */}
        <div className="flex items-center justify-center my-4">
          <div className="w-16 h-16 rounded-full bg-dark-700 flex items-center justify-center shadow-xl group-hover:shadow-neon-purple transition-all duration-300">
            <Shield className="h-8 w-8 text-primary-400 group-hover:text-primary-300 transition-all duration-300" />
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-white font-bold truncate">{name}</h3>
            {issuer && (
              <p className="text-primary-300 text-sm truncate">{issuer}</p>
            )}
          </div>
          <div className="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full h-6 w-6 group-hover:animate-pulse-slow group-hover:shadow-neon"></div>
        </div>
      </div>

      {/* Effet de brillance au survol */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 transition-opacity duration-500"></div>

      {/* Effet de bordure brillante au survol */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
        <div className="absolute -inset-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur opacity-30 group-hover:animate-pulse-slow"></div>
      </div>
    </div>
  );
};

export default OTPCardSimple;
