import React from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Lock, Eye, FileCheck, Award, CheckCircle } from 'lucide-react';

interface SecurityCertification {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  status: 'implemented' | 'verified' | 'certified';
  details: string[];
}

const SecurityCertificationsSection: React.FC = () => {
  const { t } = useTranslation(['public']);

  const certifications: SecurityCertification[] = [
    {
      icon: Shield,
      title: 'Zero-Knowledge Architecture',
      description: 'Your data is encrypted client-side before reaching our servers. We cannot access your OTP secrets.',
      status: 'implemented',
      details: [
        'AES-256 encryption',
        'Client-side key derivation',
        'No plaintext server storage',
        'User-controlled encryption keys'
      ]
    },
    {
      icon: Lock,
      title: 'End-to-End Encryption',
      description: 'All sensitive data is encrypted using military-grade encryption standards.',
      status: 'verified',
      details: [
        'TLS 1.3+ in transit',
        'AES-256 at rest',
        'PBKDF2 key derivation',
        'Secure random generation'
      ]
    },
    {
      icon: Eye,
      title: 'Privacy by Design',
      description: 'Built with privacy as a core principle, not an afterthought.',
      status: 'implemented',
      details: [
        'No tracking cookies',
        'Minimal data collection',
        'GDPR compliant',
        'User data ownership'
      ]
    },
    {
      icon: FileCheck,
      title: 'Security Audit Ready',
      description: 'Open source architecture designed for transparency and security auditing.',
      status: 'verified',
      details: [
        'Open source encryption',
        'Audit-friendly codebase',
        'Security documentation',
        'Vulnerability disclosure'
      ]
    },
    {
      icon: Award,
      title: 'Industry Standards',
      description: 'Compliant with industry security standards and best practices.',
      status: 'certified',
      details: [
        'TOTP/HOTP RFC compliance',
        'OWASP security guidelines',
        'Secure coding practices',
        'Regular security updates'
      ]
    },
    {
      icon: CheckCircle,
      title: 'Continuous Security',
      description: 'Ongoing security monitoring and improvement processes.',
      status: 'implemented',
      details: [
        'Automated security scanning',
        'Dependency monitoring',
        'Security incident response',
        'Regular security reviews'
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'implemented':
        return 'bg-green-100 text-green-800';
      case 'verified':
        return 'bg-blue-100 text-blue-800';
      case 'certified':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'implemented':
        return 'Implemented';
      case 'verified':
        return 'Verified';
      case 'certified':
        return 'Certified';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Security & Compliance
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Built with enterprise-grade security standards and transparent practices
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {certifications.map((cert, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary-100">
                  <cert.icon className="h-6 w-6 text-primary-600" />
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(cert.status)}`}>
                  {getStatusText(cert.status)}
                </span>
              </div>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {cert.title}
              </h3>
              
              <p className="text-gray-600 mb-4">
                {cert.description}
              </p>

              <div className="space-y-2">
                {cert.details.map((detail, detailIndex) => (
                  <div key={detailIndex} className="flex items-start">
                    <div className="flex-shrink-0 h-4 w-4 rounded-full bg-primary-100 flex items-center justify-center mt-0.5">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary-500"></div>
                    </div>
                    <span className="ml-3 text-sm text-gray-600">{detail}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <div className="inline-flex items-center px-6 py-3 rounded-lg bg-primary-50 border border-primary-200">
            <Shield className="h-5 w-5 text-primary-600 mr-3" />
            <span className="text-primary-800 font-medium">
              Zero-Knowledge Promise: We cannot access your encrypted data, even if we wanted to.
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityCertificationsSection;