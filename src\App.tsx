import React from 'react';
import { BrowserRouter as Router, Routes, Route, Outlet, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import AuthPage from './pages/AuthPage';
import OTPListPage from './pages/OTPListPage';
import OTPDetailPage from './pages/OTPDetailPage';
import AddOTPPage from './pages/AddOTPPage';
import UnlockPage from './pages/UnlockPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import BackupPage from './pages/BackupPage';
import ProtectedRoute from './components/ProtectedRoute';
import NotFoundPage from './pages/NotFoundPage';
import ErrorBoundary from './components/ErrorBoundary';
import { EncryptionProvider } from './contexts/EncryptionContext';
import { ToastProvider } from './contexts/ToastContext';
import SessionExpiringModal from './components/ui/SessionExpiringModal';

// Import flag-icons CSS
import 'flag-icons/css/flag-icons.min.css';

// Import public pages
import HomePage from './pages/public/HomePage';
import FeaturesPage from './pages/public/FeaturesPage';
import PricingPage from './pages/public/PricingPage';
import ContactPage from './pages/public/ContactPage';
import HelpPage from './pages/public/HelpPage';
import PrivacyPolicyPage from './pages/public/PrivacyPolicyPage';
import TermsOfServicePage from './pages/public/TermsOfServicePage';
import ChangelogPage from './pages/public/ChangelogPage';
import PublicLayout from './components/PublicLayout';

// Import admin pages
import ChangelogAdminPage from './pages/admin/ChangelogAdminPage';
import ContentAdminPage from './pages/admin/ContentAdminPage';

function App() {
  return (
    <ErrorBoundary>
      <ToastProvider>
        <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
          <Routes>
          {/* Public pages */}
          <Route element={<PublicLayout><Outlet /></PublicLayout>}>
            <Route path="/" element={<HomePage />} />
            <Route path="/features" element={<FeaturesPage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/help" element={<HelpPage />} />
            <Route path="/privacy" element={<PrivacyPolicyPage />} />
            <Route path="/terms" element={<TermsOfServicePage />} />
            <Route path="/changelog" element={<ChangelogPage />} />
          </Route>

          {/* Auth page - standalone without layout */}
          <Route path="/auth" element={<AuthPage />} />

          {/* App pages with app-specific layout */}
          <Route element={
            <EncryptionProvider sessionDuration={15} randomizeSessionDuration={true}>
              <ProtectedRoute requireEncryption={true} />
            </EncryptionProvider>
          }>
            <Route element={
              <>
                <SessionExpiringModal />
                <Layout useSEO={false}><Outlet /></Layout>
              </>
            }>
              <Route path="/app" element={<OTPListPage />} />
              <Route path="/app/otp/:id" element={<OTPDetailPage />} />
              <Route path="/app/add" element={<AddOTPPage />} />
              <Route path="/app/profile" element={<ProfilePage />} />
              <Route path="/app/settings" element={<SettingsPage />} />
              <Route path="/app/settings/backup" element={<BackupPage />} />
              <Route path="/app/admin/changelog" element={<ChangelogAdminPage />} />
              <Route path="/app/admin/content" element={<ContentAdminPage />} />
            </Route>
          </Route>
            {/* Redirection pour /app/otp/ vers /app */}
            <Route path="/app/otp/" element={<Navigate to="/app" replace />} />

            {/* Page de déverrouillage du chiffrement */}
            <Route element={
              <EncryptionProvider sessionDuration={15} randomizeSessionDuration={true}>
                <ProtectedRoute isUnlockPage={true} />
              </EncryptionProvider>
            }>
              <Route path="/app/unlock" element={
                <>
                  <SessionExpiringModal />
                  <UnlockPage />
                </>
              } />
            </Route>

            {/* Catch-all route pour les pages non trouvées */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Router>
      </ToastProvider>
    </ErrorBoundary>
  );
}

export default App;