import React from 'react';
import { LucideIcon } from 'lucide-react';

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
  iconClassName?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon: Icon,
  title,
  description,
  className = '',
  iconClassName = ''
}) => {
  return (
    <div className={`bg-dark-800 rounded-xl shadow-xl border border-dark-700 p-6 transition-all duration-300 hover:border-primary-500 group relative overflow-hidden ${className}`}>
      {/* Motif de pixels en arrière-plan */}
      <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px]"></div>
      </div>

      <div className="relative z-10">
        <div className={`rounded-full bg-primary-500 p-3 inline-flex items-center justify-center mb-4 shadow-neon ${iconClassName}`}>
          <Icon className="w-6 h-6 text-dark-900" />
        </div>
        <h3 className="text-xl font-semibold text-white mb-2 font-mono">{title}</h3>
        <p className="text-primary-300">{description}</p>
      </div>
    </div>
  );
};

export default FeatureCard;