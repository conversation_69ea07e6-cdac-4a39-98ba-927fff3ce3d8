name: Release Build

on:
  push:
    tags:
      - 'v*.*.*-alpha*'
      - 'v*.*.*-beta*'
      - 'v*.*.*'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build project
        run: npm run build
      - name: Create release archive
        run: |
          cd dist
          zip -r ../otp-manager-${{ github.ref_name }}.zip .
          cd ..
      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: otp-manager-${{ github.ref_name }}.zip
          draft: false
          prerelease: ${{ contains(github.ref, 'alpha') || contains(github.ref, 'beta') }}
