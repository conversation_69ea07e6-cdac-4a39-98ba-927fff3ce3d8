import React from 'react';
import { Check, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export interface PricingFeature {
  text: string;
  included: boolean;
}

export interface PricingPlan {
  name: string;
  price: string;
  description: string;
  features: PricingFeature[];
  buttonText: string;
  highlighted?: boolean;
  comingSoon?: boolean;
}

interface PricingCardProps {
  plan: PricingPlan;
  className?: string;
}

const PricingCard: React.FC<PricingCardProps> = ({ 
  plan, 
  className = '' 
}) => {
  const { t } = useTranslation();
  
  return (
    <div 
      className={`relative rounded-xl overflow-hidden border ${plan.highlighted ? 'border-primary-500 shadow-xl' : 'border-dark-200'} ${className}`}
    >
      {plan.highlighted && (
        <div className="absolute top-0 right-0 bg-primary-500 text-white px-4 py-1 rounded-bl-lg text-sm font-medium">
          {t('pricing.popular')}
        </div>
      )}
      
      {plan.comingSoon && (
        <div className="absolute top-0 right-0 bg-dark-500 text-white px-4 py-1 rounded-bl-lg text-sm font-medium">
          {t('pricing.comingSoon')}
        </div>
      )}
      
      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
        <div className="mb-4">
          <span className="text-3xl font-bold">{plan.price}</span>
          {plan.price !== 'Free' && <span className="text-dark-500 ml-1">/month</span>}
        </div>
        <p className="text-dark-600 mb-6">{plan.description}</p>
        
        <button 
          className={`w-full py-2 px-4 rounded-lg mb-6 ${
            plan.highlighted 
              ? 'bg-primary-500 text-white hover:bg-primary-600' 
              : plan.comingSoon 
                ? 'bg-dark-300 text-white cursor-not-allowed' 
                : 'bg-dark-100 text-dark-900 hover:bg-dark-200'
          } transition-colors`}
          disabled={plan.comingSoon}
        >
          {plan.buttonText}
        </button>
        
        <div className="space-y-3">
          {plan.features.map((feature, index) => (
            <div key={index} className="flex items-start">
              <div className={`mt-1 rounded-full p-1 ${feature.included ? 'text-primary-500 bg-primary-100' : 'text-dark-400 bg-dark-100'}`}>
                <Check className="h-3 w-3" />
              </div>
              <span className={`ml-3 text-sm ${feature.included ? 'text-dark-800' : 'text-dark-400 line-through'}`}>
                {feature.text}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingCard; 