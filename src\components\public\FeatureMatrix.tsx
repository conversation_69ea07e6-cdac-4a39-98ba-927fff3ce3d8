import React from 'react';
import { Check, X } from 'lucide-react';
import FeatureStatusIndicator, { FeatureStatus } from './FeatureStatusIndicator';

interface FeatureMatrixItem {
  name: string;
  description?: string;
  free: {
    available: boolean;
    status: FeatureStatus;
  };
  pro: {
    available: boolean;
    status: FeatureStatus;
  };
  enterprise: {
    available: boolean;
    status: FeatureStatus;
  };
}

const FeatureMatrix: React.FC = () => {
  const features: FeatureMatrixItem[] = [
    {
      name: 'OTP Storage',
      description: 'Store unlimited TOTP/HOTP codes',
      free: { available: true, status: 'available' },
      pro: { available: true, status: 'available' },
      enterprise: { available: true, status: 'available' }
    },
    {
      name: 'Zero-Knowledge Encryption',
      description: 'Client-side AES-256 encryption',
      free: { available: true, status: 'available' },
      pro: { available: true, status: 'available' },
      enterprise: { available: true, status: 'available' }
    },
    {
      name: 'Multi-Device Sync',
      description: 'Sync across all your devices',
      free: { available: true, status: 'available' },
      pro: { available: true, status: 'available' },
      enterprise: { available: true, status: 'available' }
    },
    {
      name: 'Backup & Recovery',
      description: 'Encrypted backup system',
      free: { available: true, status: 'available' },
      pro: { available: true, status: 'available' },
      enterprise: { available: true, status: 'available' }
    },
    {
      name: 'Basic API Access',
      description: 'REST API for integrations',
      free: { available: true, status: 'available' },
      pro: { available: true, status: 'available' },
      enterprise: { available: true, status: 'available' }
    },
    {
      name: 'Team Sharing',
      description: 'Share OTPs with team members',
      free: { available: false, status: 'planned' },
      pro: { available: true, status: 'development' },
      enterprise: { available: true, status: 'development' }
    },
    {
      name: 'Advanced Analytics',
      description: 'Usage insights and reporting',
      free: { available: false, status: 'planned' },
      pro: { available: true, status: 'development' },
      enterprise: { available: true, status: 'development' }
    },
    {
      name: 'Priority Support',
      description: 'Email and chat support',
      free: { available: false, status: 'planned' },
      pro: { available: true, status: 'development' },
      enterprise: { available: true, status: 'development' }
    },
    {
      name: 'SSO Integration',
      description: 'Single sign-on with SAML/OAuth',
      free: { available: false, status: 'planned' },
      pro: { available: false, status: 'planned' },
      enterprise: { available: true, status: 'planned' }
    },
    {
      name: 'Admin Dashboard',
      description: 'Advanced admin controls',
      free: { available: false, status: 'planned' },
      pro: { available: false, status: 'planned' },
      enterprise: { available: true, status: 'development' }
    },
    {
      name: 'Compliance Reporting',
      description: 'Audit logs and compliance tools',
      free: { available: false, status: 'planned' },
      pro: { available: false, status: 'planned' },
      enterprise: { available: true, status: 'planned' }
    },
    {
      name: 'Custom Integrations',
      description: 'Dedicated integration support',
      free: { available: false, status: 'planned' },
      pro: { available: false, status: 'planned' },
      enterprise: { available: true, status: 'planned' }
    }
  ];

  const renderFeatureCell = (feature: { available: boolean; status: FeatureStatus }) => {
    if (feature.available) {
      return (
        <div className="flex items-center justify-center gap-2">
          <Check className="h-5 w-5 text-green-600" />
          {feature.status !== 'available' && (
            <FeatureStatusIndicator status={feature.status} showText={false} className="text-xs" />
          )}
        </div>
      );
    } else {
      return (
        <div className="flex items-center justify-center">
          <X className="h-5 w-5 text-gray-400" />
        </div>
      );
    }
  };

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Detailed Feature Comparison
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Compare all features across our pricing tiers. Features marked as "In Development" or "Planned" 
            are not yet available but are on our roadmap.
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">
                  Feature
                </th>
                <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 border-b border-gray-200">
                  Free
                </th>
                <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 border-b border-gray-200 bg-primary-50">
                  Pro
                </th>
                <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 border-b border-gray-200">
                  Enterprise
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {features.map((feature, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 border-b border-gray-200">
                    <div>
                      <div className="font-medium text-gray-900">{feature.name}</div>
                      {feature.description && (
                        <div className="text-sm text-gray-600 mt-1">{feature.description}</div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-center border-b border-gray-200">
                    {renderFeatureCell(feature.free)}
                  </td>
                  <td className="px-6 py-4 text-center border-b border-gray-200 bg-primary-25">
                    {renderFeatureCell(feature.pro)}
                  </td>
                  <td className="px-6 py-4 text-center border-b border-gray-200">
                    {renderFeatureCell(feature.enterprise)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-6 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <FeatureStatusIndicator status="available" showText={false} />
              <span>Available Now</span>
            </div>
            <div className="flex items-center gap-2">
              <FeatureStatusIndicator status="development" showText={false} />
              <span>In Development</span>
            </div>
            <div className="flex items-center gap-2">
              <FeatureStatusIndicator status="planned" showText={false} />
              <span>Planned</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureMatrix;