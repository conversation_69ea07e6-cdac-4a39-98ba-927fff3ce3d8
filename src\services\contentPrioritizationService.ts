/**
 * Content Prioritization Service
 * Implements intelligent algorithms for dynamic card sizing and positioning
 * based on content importance, user behavior, and accessibility requirements
 */

export interface ContentMetrics {
  // Content characteristics
  wordCount: number;
  hasMedia: boolean;
  hasInteractiveElements: boolean;
  complexity: 'low' | 'medium' | 'high';
  
  // User engagement metrics
  viewCount: number;
  interactionCount: number;
  timeSpent: number; // in seconds
  lastAccessed: Date;
  
  // Business importance
  businessPriority: 1 | 2 | 3 | 4 | 5; // 1 = highest
  conversionValue: number; // 0-100
  
  // Accessibility considerations
  requiresLargeText: boolean;
  requiresHighContrast: boolean;
  keyboardNavigable: boolean;
}

export interface PrioritizationContext {
  // User preferences
  userType: 'new' | 'returning' | 'power';
  deviceType: 'mobile' | 'tablet' | 'desktop';
  screenSize: { width: number; height: number };
  
  // Current session
  sessionDuration: number;
  currentFocus: string | null;
  
  // Accessibility needs
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  usesScreenReader: boolean;
  
  // Layout constraints
  availableSpace: { width: number; height: number };
  gridColumns: number;
  maxCards: number;
}

export interface PrioritizedContent {
  id: string;
  priority: 1 | 2 | 3 | 4 | 5;
  weight: number; // 0-100
  recommendedSize: {
    columns: number;
    rows: number;
  };
  position?: {
    column: number;
    row: number;
  };
  sticky: boolean;
  animationDelay: number;
  accessibilityEnhancements: string[];
}

export class ContentPrioritizationService {
  private static instance: ContentPrioritizationService;
  private prioritizationCache = new Map<string, PrioritizedContent>();
  private userBehaviorHistory: Map<string, ContentMetrics> = new Map();

  static getInstance(): ContentPrioritizationService {
    if (!ContentPrioritizationService.instance) {
      ContentPrioritizationService.instance = new ContentPrioritizationService();
    }
    return ContentPrioritizationService.instance;
  }

  /**
   * Calculate content priority based on multiple factors
   */
  calculatePriority(
    contentId: string,
    metrics: ContentMetrics,
    context: PrioritizationContext
  ): PrioritizedContent {
    // Check cache first
    const cached = this.prioritizationCache.get(contentId);
    if (cached && this.isCacheValid(cached, metrics)) {
      return cached;
    }

    // Calculate base priority score
    let priorityScore = this.calculateBasePriority(metrics, context);
    
    // Apply user behavior adjustments
    priorityScore = this.applyUserBehaviorAdjustments(priorityScore, metrics, context);
    
    // Apply accessibility adjustments
    priorityScore = this.applyAccessibilityAdjustments(priorityScore, metrics, context);
    
    // Apply device-specific adjustments
    priorityScore = this.applyDeviceAdjustments(priorityScore, context);

    // Convert score to priority level (1-5)
    const priority = this.scoreToPriorityLevel(priorityScore);
    
    // Calculate weight within priority level
    const weight = this.calculateWeight(priorityScore, priority);
    
    // Determine recommended size
    const recommendedSize = this.calculateRecommendedSize(priority, weight, metrics, context);
    
    // Determine if content should be sticky
    const sticky = this.shouldBeSticky(metrics, context, priority);
    
    // Calculate animation delay for staggered animations
    const animationDelay = this.calculateAnimationDelay(priority, context);
    
    // Determine accessibility enhancements
    const accessibilityEnhancements = this.getAccessibilityEnhancements(metrics, context);

    const result: PrioritizedContent = {
      id: contentId,
      priority,
      weight,
      recommendedSize,
      sticky,
      animationDelay,
      accessibilityEnhancements,
    };

    // Cache the result
    this.prioritizationCache.set(contentId, result);
    
    return result;
  }

  /**
   * Calculate base priority score (0-100)
   */
  private calculateBasePriority(metrics: ContentMetrics, context: PrioritizationContext): number {
    let score = 50; // Start with neutral score

    // Business priority weight (40% of total score)
    const businessWeight = (6 - metrics.businessPriority) * 8; // 8, 16, 24, 32, 40
    score += businessWeight * 0.4;

    // Conversion value weight (20% of total score)
    score += metrics.conversionValue * 0.2;

    // User engagement weight (25% of total score)
    const engagementScore = this.calculateEngagementScore(metrics);
    score += engagementScore * 0.25;

    // Content complexity weight (15% of total score)
    const complexityScore = metrics.complexity === 'high' ? 15 : 
                           metrics.complexity === 'medium' ? 10 : 5;
    score += complexityScore * 0.15;

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Calculate user engagement score
   */
  private calculateEngagementScore(metrics: ContentMetrics): number {
    let score = 0;

    // View count (normalized)
    score += Math.min(20, metrics.viewCount / 10);

    // Interaction count (normalized)
    score += Math.min(30, metrics.interactionCount * 2);

    // Time spent (normalized, max 30 points for 5+ minutes)
    score += Math.min(30, metrics.timeSpent / 10);

    // Recency bonus (max 20 points for content accessed today)
    const daysSinceAccess = (Date.now() - metrics.lastAccessed.getTime()) / (1000 * 60 * 60 * 24);
    const recencyScore = Math.max(0, 20 - daysSinceAccess * 2);
    score += recencyScore;

    return Math.min(100, score);
  }

  /**
   * Apply user behavior adjustments
   */
  private applyUserBehaviorAdjustments(
    score: number,
    metrics: ContentMetrics,
    context: PrioritizationContext
  ): number {
    let adjustedScore = score;

    // New users: prioritize onboarding and simple content
    if (context.userType === 'new') {
      if (metrics.complexity === 'low') adjustedScore += 10;
      if (metrics.businessPriority <= 2) adjustedScore += 15;
    }

    // Power users: prioritize advanced features and efficiency
    if (context.userType === 'power') {
      if (metrics.hasInteractiveElements) adjustedScore += 10;
      if (metrics.complexity === 'high') adjustedScore += 5;
    }

    // Session duration adjustments
    if (context.sessionDuration > 300) { // 5+ minutes
      // Prioritize fresh content for long sessions
      const daysSinceAccess = (Date.now() - metrics.lastAccessed.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceAccess > 1) adjustedScore += 8;
    }

    return Math.min(100, Math.max(0, adjustedScore));
  }

  /**
   * Apply accessibility adjustments
   */
  private applyAccessibilityAdjustments(
    score: number,
    metrics: ContentMetrics,
    context: PrioritizationContext
  ): number {
    let adjustedScore = score;

    // Screen reader users: prioritize keyboard navigable content
    if (context.usesScreenReader && metrics.keyboardNavigable) {
      adjustedScore += 15;
    }

    // High contrast preference: prioritize text-heavy content
    if (context.prefersHighContrast && !metrics.hasMedia) {
      adjustedScore += 10;
    }

    // Reduced motion preference: prioritize static content
    if (context.prefersReducedMotion && !metrics.hasInteractiveElements) {
      adjustedScore += 5;
    }

    return Math.min(100, Math.max(0, adjustedScore));
  }

  /**
   * Apply device-specific adjustments
   */
  private applyDeviceAdjustments(score: number, context: PrioritizationContext): number {
    let adjustedScore = score;

    // Mobile devices: prioritize essential content
    if (context.deviceType === 'mobile') {
      // Reduce score for media-heavy content on mobile
      adjustedScore *= 0.9;
    }

    // Small screens: boost high-priority content more
    if (context.screenSize.width < 768) {
      if (score > 70) adjustedScore += 10;
    }

    return Math.min(100, Math.max(0, adjustedScore));
  }

  /**
   * Convert priority score to level (1-5)
   */
  private scoreToPriorityLevel(score: number): 1 | 2 | 3 | 4 | 5 {
    if (score >= 80) return 1;
    if (score >= 65) return 2;
    if (score >= 45) return 3;
    if (score >= 25) return 4;
    return 5;
  }

  /**
   * Calculate weight within priority level (0-100)
   */
  private calculateWeight(score: number, priority: number): number {
    const ranges = {
      1: { min: 80, max: 100 },
      2: { min: 65, max: 79 },
      3: { min: 45, max: 64 },
      4: { min: 25, max: 44 },
      5: { min: 0, max: 24 },
    };

    const range = ranges[priority as keyof typeof ranges];
    const normalizedScore = ((score - range.min) / (range.max - range.min)) * 100;
    return Math.min(100, Math.max(0, normalizedScore));
  }

  /**
   * Calculate recommended size based on priority and content
   */
  private calculateRecommendedSize(
    priority: number,
    weight: number,
    metrics: ContentMetrics,
    context: PrioritizationContext
  ): { columns: number; rows: number } {
    // Base sizes by priority
    const baseSizes = {
      1: { columns: 6, rows: 3 },
      2: { columns: 4, rows: 2 },
      3: { columns: 3, rows: 2 },
      4: { columns: 2, rows: 1 },
      5: { columns: 2, rows: 1 },
    };

    let size = baseSizes[priority as keyof typeof baseSizes];

    // Adjust for content characteristics
    if (metrics.hasMedia && priority <= 2) {
      size.rows += 1;
    }

    if (metrics.wordCount > 200 && priority <= 3) {
      size.columns = Math.min(size.columns + 1, context.gridColumns);
    }

    // Adjust for device constraints
    if (context.deviceType === 'mobile') {
      size.columns = Math.min(size.columns, 4);
    }

    // Accessibility adjustments
    if (context.prefersHighContrast || metrics.requiresLargeText) {
      size.rows = Math.max(size.rows, 2);
    }

    return size;
  }

  /**
   * Determine if content should be sticky
   */
  private shouldBeSticky(
    metrics: ContentMetrics,
    context: PrioritizationContext,
    priority: number
  ): boolean {
    // Only high-priority content can be sticky
    if (priority > 2) return false;

    // Business-critical content
    if (metrics.businessPriority === 1 && metrics.conversionValue > 80) return true;

    // Frequently accessed content
    if (metrics.interactionCount > 50 && metrics.viewCount > 100) return true;

    // Navigation or control elements
    if (metrics.hasInteractiveElements && metrics.complexity === 'low') return true;

    return false;
  }

  /**
   * Calculate animation delay for staggered animations
   */
  private calculateAnimationDelay(priority: number, context: PrioritizationContext): number {
    if (context.prefersReducedMotion) return 0;

    // Higher priority = less delay
    const baseDelay = (priority - 1) * 50; // 0ms, 50ms, 100ms, 150ms, 200ms
    
    // Add randomization for natural feel
    const randomOffset = Math.random() * 30;
    
    return baseDelay + randomOffset;
  }

  /**
   * Get accessibility enhancements for content
   */
  private getAccessibilityEnhancements(
    metrics: ContentMetrics,
    context: PrioritizationContext
  ): string[] {
    const enhancements: string[] = [];

    if (context.usesScreenReader) {
      enhancements.push('enhanced-aria-labels');
      enhancements.push('skip-links');
    }

    if (context.prefersHighContrast) {
      enhancements.push('high-contrast-mode');
    }

    if (metrics.requiresLargeText) {
      enhancements.push('large-text-support');
    }

    if (context.prefersReducedMotion) {
      enhancements.push('reduced-motion');
    }

    if (!metrics.keyboardNavigable) {
      enhancements.push('keyboard-navigation-warning');
    }

    return enhancements;
  }

  /**
   * Check if cached result is still valid
   */
  private isCacheValid(cached: PrioritizedContent, currentMetrics: ContentMetrics): boolean {
    // Cache is valid for 5 minutes for frequently changing content
    // or 1 hour for stable content
    const cacheAge = Date.now() - (this.userBehaviorHistory.get(cached.id)?.lastAccessed.getTime() || 0);
    const maxAge = currentMetrics.interactionCount > 10 ? 5 * 60 * 1000 : 60 * 60 * 1000;
    
    return cacheAge < maxAge;
  }

  /**
   * Update user behavior history
   */
  updateUserBehavior(contentId: string, metrics: Partial<ContentMetrics>): void {
    const existing = this.userBehaviorHistory.get(contentId);
    if (existing) {
      this.userBehaviorHistory.set(contentId, { ...existing, ...metrics });
    } else {
      this.userBehaviorHistory.set(contentId, {
        wordCount: 0,
        hasMedia: false,
        hasInteractiveElements: false,
        complexity: 'medium',
        viewCount: 0,
        interactionCount: 0,
        timeSpent: 0,
        lastAccessed: new Date(),
        businessPriority: 3,
        conversionValue: 50,
        requiresLargeText: false,
        requiresHighContrast: false,
        keyboardNavigable: true,
        ...metrics,
      });
    }

    // Invalidate cache for this content
    this.prioritizationCache.delete(contentId);
  }

  /**
   * Clear cache (useful for testing or major layout changes)
   */
  clearCache(): void {
    this.prioritizationCache.clear();
  }

  /**
   * Get prioritization analytics
   */
  getAnalytics(): {
    cacheSize: number;
    behaviorHistorySize: number;
    priorityDistribution: Record<number, number>;
  } {
    const priorityDistribution: Record<number, number> = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    
    for (const content of this.prioritizationCache.values()) {
      priorityDistribution[content.priority]++;
    }

    return {
      cacheSize: this.prioritizationCache.size,
      behaviorHistorySize: this.userBehaviorHistory.size,
      priorityDistribution,
    };
  }
}

// Export singleton instance
export const contentPrioritization = ContentPrioritizationService.getInstance();