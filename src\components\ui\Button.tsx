import React, { ButtonHTMLAttributes } from 'react';
import { Loader2 } from 'lucide-react';
import { designUtils, componentVariants, type ButtonVariant } from '../../lib/designSystem';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant | 'outline' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  fullWidth = false,
  className = '',
  disabled,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all focus:outline-none focus-visible:outline-2 focus-visible:outline-offset-2';
  
  const variantClasses = {
    primary: componentVariants.button.primary,
    secondary: componentVariants.button.secondary,
    outline: 'bg-transparent border-2 border-blue-500 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg px-6 py-3 font-medium transition-all hover:-translate-y-1 focus-visible:outline-blue-500',
    dark: 'bg-navy-800 hover:bg-navy-700 text-white border border-navy-700 rounded-lg px-6 py-3 font-medium transition-all hover:-translate-y-1 shadow-md hover:shadow-lg focus-visible:outline-navy-500',
  };
  
  const sizeClasses = {
    sm: 'text-sm px-4 py-2',
    md: 'px-6 py-3',
    lg: 'text-lg px-8 py-4',
  };
  
  const widthClass = fullWidth ? 'w-full' : '';
  const disabledClass = disabled || loading ? 'opacity-50 cursor-not-allowed transform-none pointer-events-none' : '';
  
  return (
    <button
      className={designUtils.cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        widthClass,
        disabledClass,
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <Loader2 className="animate-spin h-5 w-5 mr-2" />
      ) : icon ? (
        <span className="mr-2">{icon}</span>
      ) : null}
      {children}
    </button>
  );
};

export default Button;
