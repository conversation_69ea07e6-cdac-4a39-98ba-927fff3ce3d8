import { useState, useEffect } from 'react';
import { themeManager, type Theme } from '../lib/designSystem';

/**
 * React hook for managing theme state and transitions
 */
export function useTheme() {
  const [theme, setTheme] = useState<Theme>(() => themeManager.getTheme());
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    // Initialize theme on mount
    themeManager.initialize();
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      if (!localStorage.getItem('theme')) {
        const newTheme = e.matches ? 'dark' : 'light';
        setTheme(newTheme);
        themeManager.setTheme(newTheme);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  const toggleTheme = () => {
    setIsTransitioning(true);
    
    // Add transition class to body for smooth theme change
    document.body.classList.add('theme-transition');
    
    const newTheme = themeManager.toggleTheme();
    setTheme(newTheme);
    
    // Remove transition class after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
      document.body.classList.remove('theme-transition');
    }, 250);
  };

  const setThemeDirectly = (newTheme: Theme) => {
    if (newTheme === theme) return;
    
    setIsTransitioning(true);
    document.body.classList.add('theme-transition');
    
    themeManager.setTheme(newTheme);
    setTheme(newTheme);
    
    setTimeout(() => {
      setIsTransitioning(false);
      document.body.classList.remove('theme-transition');
    }, 250);
  };

  return {
    theme,
    toggleTheme,
    setTheme: setThemeDirectly,
    isTransitioning,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  };
}