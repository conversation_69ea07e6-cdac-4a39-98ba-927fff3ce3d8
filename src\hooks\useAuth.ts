import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { User, AuthError } from '@supabase/supabase-js';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Vérifier la session au chargement initial
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user ?? null);
      } catch (error) {
        // Gérer les erreurs de session silencieusement
        console.error('Session check error:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // S'abonner aux changements d'état d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'TOKEN_REFRESHED') {
        setUser(session?.user ?? null);
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        navigate('/auth');
      } else {
        const newUser = session?.user ?? null;
        setUser(newUser);

        // Rediriger vers la page précédente ou la page par défaut
        if (newUser && window.location.pathname === '/auth') {
          const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/app';
          // Nettoyer le stockage
          sessionStorage.removeItem('redirectAfterLogin');
          navigate(redirectPath);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate]);

  const signIn = async (email: string, password: string) => {
    return safeAuthCall(async () => {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
    });
  };

  const signUp = async (email: string, password: string, firstName: string) => {
    return safeAuthCall(async () => {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
          },
        },
      });
      if (error) throw error;
    });
  };

  const signInWithProvider = async (provider: 'google' | 'github' | 'facebook' | 'twitter' | 'linkedin') => {
    return safeAuthCall(async () => {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });
      if (error) throw error;
    });
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      navigate('/');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Force navigation even if sign out fails
      navigate('/');
    }
  };

  // Fonction pour gérer les erreurs d'authentification
  const handleAuthError = (error: unknown) => {
    // Vérifier si c'est une erreur de token de rafraîchissement
    if (error instanceof AuthError &&
        (error.message.includes('Invalid Refresh Token') ||
         error.message.includes('Refresh Token Not Found'))) {
      // Forcer la déconnexion en cas d'erreur de token
      signOut().catch(console.error);
      return new Error('Votre session a expiré. Veuillez vous reconnecter.');
    }
    // Retourner l'erreur originale pour les autres cas
    return error instanceof Error ? error : new Error('Une erreur est survenue');
  };

  // Wrapper pour les méthodes d'authentification avec gestion d'erreur
  const safeAuthCall = async <T>(authCall: () => Promise<T>): Promise<T> => {
    try {
      return await authCall();
    } catch (error) {
      throw handleAuthError(error);
    }
  };

  // Helper method to redirect to the app dashboard
  const goToDashboard = () => {
    navigate('/app');
  };

  return {
    user,
    loading,
    signIn,
    signUp,
    signInWithProvider,
    signOut,
    goToDashboard
  };
}