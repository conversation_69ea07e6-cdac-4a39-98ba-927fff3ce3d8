import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Copy, Trash2, <PERSON><PERSON>eft, Loader2, Check<PERSON>ircle, AlertCircle, Clock, Lock } from 'lucide-react';
import DeleteConfirmationModal from '../components/ui/DeleteConfirmationModal';
import * as OTPAuth from 'otpauth';
import { supabase } from '../lib/supabase';
import type { Database } from '../lib/database.types';
import { useEncryption } from '../contexts/EncryptionContext';
import { useToast } from '../contexts/ToastContext';

type OTPSecret = Database['public']['Tables']['otp_secrets']['Row'];

import SEO from '../components/SEO';

const OTPDetailPage: React.FC = () => {
  const { t } = useTranslation(['common', 'otp', 'encryption']);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isEncryptionSetup, isUnlocked, decryptData } = useEncryption();
  const { showToast } = useToast();
  const [secret, setSecret] = useState<OTPSecret | null>(null);
  const [decryptedSecret, setDecryptedSecret] = useState<string | null>(null);
  const [currentCode, setCurrentCode] = useState<string>('');
  const [timeRemaining, setTimeRemaining] = useState<number>(30);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Rediriger vers la page de déverrouillage si le chiffrement est configuré mais pas déverrouillé
  // La redirection est maintenant gérée par le composant ProtectedRoute

  // Vérifier s'il y a un message de succès dans sessionStorage
  useEffect(() => {
    const successMessage = sessionStorage.getItem('otpAddSuccess');
    if (successMessage) {
      // Afficher le message de succès avec le composant Toast
      showToast(successMessage, 'success');

      // Supprimer le message pour éviter qu'il ne s'affiche à nouveau lors d'un rafraîchissement
      sessionStorage.removeItem('otpAddSuccess');
    }
  }, [showToast]);

  useEffect(() => {
    const fetchSecret = async () => {
      try {
        const { data, error } = await supabase
          .from('otp_secrets')
          .select('*')
          .eq('id', id)
          .single();

        if (error) throw error;
        if (!data) throw new Error('Secret not found');

        setSecret(data);

        // Déchiffrer le secret si le chiffrement est configuré et déverrouillé
        if (isUnlocked && data.encrypted_secret && data.secret_iv) {
          try {
            const decrypted = await decryptData(data.encrypted_secret, data.secret_iv);
            if (!decrypted) throw new Error(t('encryption:decryptionFailed'));
            setDecryptedSecret(decrypted);
          } catch (decryptError) {
            const errorMessage = t('encryption:decryptionFailed');
            setError(errorMessage);
            showToast(errorMessage, 'error');
          }
        } else if (data.secret && data.secret !== "ENCRYPTED") {
          // Utiliser le secret en clair si disponible et si ce n'est pas la valeur factice
          setDecryptedSecret(data.secret);
        } else if (!isUnlocked) {
          // Si le chiffrement est configuré mais pas déverrouillé
          navigate('/app/unlock');
        } else {
          throw new Error('No secret available');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load OTP code';
        setError(errorMessage);
        showToast(errorMessage, 'error');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchSecret();
    }
  }, [id, isUnlocked, decryptData, navigate, t, showToast]);

  useEffect(() => {
    if (!secret || !decryptedSecret) return;

    try {
      const totp = new OTPAuth.TOTP({
        issuer: secret.issuer || '',
        label: secret.name,
        algorithm: 'SHA1',
        digits: 6,
        period: 30,
        secret: OTPAuth.Secret.fromBase32(decryptedSecret)
      });

      const updateCode = () => {
        const code = totp.generate();
        setCurrentCode(code);

        const epoch = Math.floor(Date.now() / 1000);
        const remaining = 30 - (epoch % 30);
        setTimeRemaining(remaining);
      };

      updateCode();
      const interval = setInterval(updateCode, 1000);

      return () => clearInterval(interval);
    } catch (err) {
      const errorMessage = 'Invalid OTP secret format';
      setError(errorMessage);
      showToast(errorMessage, 'error');
      setLoading(false);
    }
  }, [secret, decryptedSecret, showToast]);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(currentCode);
    setCopied(true);
    showToast(t('otp:codeCopied'), 'success');
    setTimeout(() => setCopied(false), 2000);
  };

  const openDeleteModal = () => {
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };

  const handleDelete = async () => {
    try {
      const { error } = await supabase
        .from('otp_secrets')
        .delete()
        .eq('id', id);

      if (error) throw error;
      showToast(t('otp:deleteSuccess'), 'success');
      navigate('/app');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete OTP code';
      setError(errorMessage);
      showToast(errorMessage, 'error');
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh]">
        <Loader2 className="h-12 w-12 text-primary-500 animate-spin mb-4" />
        <div className="text-dark-600 font-medium">{t('common:loading')}</div>
      </div>
    );
  }

  if (error || !secret) {
    return (
      <div className="bg-red-900/30 border border-red-800 text-red-400 p-6 rounded-xl shadow-xl flex items-center">
        <AlertCircle className="h-6 w-6 mr-3 flex-shrink-0" />
        <p className="font-medium">{error || 'OTP code not found'}</p>
      </div>
    );
  }

  return (
    <div>
      {secret && (
        <SEO title={secret.name} description={`OTP code for ${secret.name}`} />
      )}
      <button
        onClick={() => navigate('/app')}
        className="mb-8 inline-flex items-center text-primary-400 hover:text-primary-300 transition-colors duration-200 font-medium"
      >
        <ArrowLeft className="h-5 w-5 mr-2" />
        {t('otp:list')}
      </button>

      <div className="bg-dark-800 backdrop-blur-sm rounded-3xl shadow-xl p-8 border border-dark-700 relative overflow-hidden">
        {/* Motif de pixels en arrière-plan */}
        <div className="absolute inset-0 opacity-10 transition-opacity duration-300">
          <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px]"></div>
        </div>
        <div className="flex justify-between items-start relative z-10">
          <div>
            <h1 className="text-3xl font-bold text-white font-mono">{secret.name}</h1>
            {secret.issuer && (
              <p className="text-primary-400 mt-2 text-lg">{secret.issuer}</p>
            )}
          </div>
          <button
            onClick={openDeleteModal}
            className="text-red-400 hover:text-red-300 p-2 hover:bg-red-900/30 rounded-full transition-colors duration-200"
            aria-label={t('otp:delete')}
          >
            <Trash2 className="h-6 w-6" />
          </button>
        </div>

        <div className="mt-10">
          <div className="text-center py-10 bg-dark-900 rounded-2xl shadow-xl border border-dark-700 relative overflow-hidden">
            {/* Motif de pixels en arrière-plan */}
            <div className="absolute inset-0 opacity-10 transition-opacity duration-300">
              <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px]"></div>
            </div>

            <div className="relative z-10">
            <div className="text-6xl font-mono font-bold tracking-widest mb-8 text-primary-500 transform hover:scale-105 transition-all duration-300">
              {currentCode.replace(/(\d{3})(\d{3})/, '$1 $2').split('').map((digit, index) => (
                <span
                  key={index}
                  className="inline-block mx-1 transition-all duration-300 hover:text-primary-400 hover:scale-110"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {digit === ' ' ? <span className="mx-2"></span> : digit}
                </span>
              ))}
            </div>
            <div className="mt-6">
              <div className="w-full bg-dark-700 h-4 rounded-full overflow-hidden shadow-inner mx-auto max-w-xs border border-dark-600">
                <div
                  className="bg-gradient-to-r from-primary-500 to-secondary-500 h-4 rounded-full transition-all duration-1000 ease-linear relative overflow-hidden"
                  style={{ width: `${(timeRemaining / 30) * 100}%` }}
                >
                  <div className="absolute inset-0 bg-white/20 animate-pulse-slow"></div>
                </div>
              </div>
              <div className="text-sm font-medium text-primary-400 mt-4 flex items-center justify-center bg-dark-800/50 py-2 px-4 rounded-full inline-flex mx-auto">
                <Clock className="h-5 w-5 mr-2 text-primary-400 animate-pulse-slow" />
                <span className="font-mono">{t('otp:refreshIn', { seconds: timeRemaining })}</span>
              </div>
            </div>
            </div>
          </div>

          <button
            onClick={handleCopy}
            className={`mt-8 w-full flex items-center justify-center px-6 py-3 rounded-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 ${
              copied
                ? 'bg-green-900/30 border border-green-800 text-green-400 shadow-xl'
                : 'bg-primary-500 text-dark-900 font-bold hover:shadow-neon'
            }`}
          >
            {copied ? (
              <>
                <CheckCircle className="h-5 w-5 mr-2" />
                <span className="font-medium">{t('common:success')}</span>
              </>
            ) : (
              <>
                <Copy className="h-5 w-5 mr-2" />
                <span className="font-medium">{t('otp:copy')}</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Modal de confirmation de suppression */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDelete}
        itemName={secret?.name || ''}
        itemType={t('otp:platform')}
      />
    </div>
  );
};

export default OTPDetailPage;