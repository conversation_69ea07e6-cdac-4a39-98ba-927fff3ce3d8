import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Shield<PERSON>he<PERSON>, ArrowRight, Play } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

const ImmersiveHeroSection: React.FC = () => {
  const { t } = useTranslation(['public']);
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      navigate('/app');
    } else {
      navigate('/auth');
    }
  };

  const handleWatchDemo = () => {
    navigate('/features');
  };

  return (
    <section className="relative bg-gradient-to-br from-white via-slate-50 to-blue-50 overflow-hidden">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" />
        <div className="absolute bottom-20 left-20 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '2s' }} />
      </div>

      {/* Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="pt-5 pb-6 lg:pt-8 lg:pb-6">
          <div className="text-center max-w-4xl mx-auto">
            {/* Trust Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 border border-green-200 rounded-full text-green-700 text-sm font-medium mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              Zero-Knowledge Security
            </div>

            {/* Main Headline */}
            <h1 className="text-xl sm:text-2xl lg:text-4xl font-bold text-gray-900 leading-tight mb-2">
              Secure OTP Management
              <span className="block text-blue-600">
                For Everyone
              </span>
            </h1>

            <p className="text-gray-600 max-w-2xl mx-auto leading-relaxed mb-10">
              Cloud-based two-factor authentication with zero-knowledge encryption.
              From individuals to enterprises, your secrets stay private, even from us.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-2">
              <button
                onClick={handleGetStarted}
                className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <ShieldCheck className="w-5 h-5" />
                {user ? 'Go to Dashboard' : 'Get Started Free'}
                <ArrowRight className="w-auto h-auto" />
              </button>

              <button
                onClick={handleWatchDemo}
                className="w-full sm:w-auto bg-white hover:bg-gray-50 text-gray-700 font-semibold px-4 py-4 rounded-lg border border-gray-200 transition-all duration-200 flex items-center justify-center gap-3 hover:shadow-md"
              >
                <Play className="w-auto h-auto" />
                Watch Demo
              </button>
            </div>

            {/* Security Compliance */}
            <div className="text-center">
              <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                <div className="text-gray-400 font-medium text-sm">GDPR Compliant</div>
                <div className="text-gray-400 font-medium text-sm">99.99% Uptime</div>
              </div>
            </div>
          </div>

          {/* Hero Visual */}
          <div className="mt-16 max-w-5xl mx-auto">
            <div className="relative">
              {/* Main Dashboard Preview */}
              <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-red-400 rounded-full" />
                    <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                    <div className="w-3 h-3 bg-green-400 rounded-full" />
                    <div className="ml-4 text-sm text-gray-500 font-mono">SecureOTP Dashboard</div>
                  </div>
                </div>
                <div className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Security Status */}
                    <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                          <ShieldCheck className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">Security Status</div>
                          <div className="text-sm text-green-600">All systems secure</div>
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-green-600">98%</div>
                      <div className="text-sm text-gray-600">Security Score</div>
                    </div>

                    {/* Active OTPs */}
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                          <div className="w-5 h-5 bg-white rounded text-blue-500 flex items-center justify-center text-xs font-bold">2FA</div>
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">Active OTPs</div>
                          <div className="text-sm text-blue-600">Synchronized</div>
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">24</div>
                      <div className="text-sm text-gray-600">Services Protected</div>
                    </div>

                    {/* Team Access */}
                    <div className="bg-purple-50 border border-purple-200 rounded-xl p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                          <div className="w-5 h-5 text-white text-xs">👥</div>
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">Team Access</div>
                          <div className="text-sm text-purple-600">5 members online</div>
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-purple-600">12</div>
                      <div className="text-sm text-gray-600">Shared Secrets</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Security Badge */}
              <div className="absolute -top-4 -right-4 bg-white border border-gray-200 rounded-xl p-4 shadow-lg">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="font-medium text-gray-700">256-bit AES</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ImmersiveHeroSection;