# Agent Guidelines for OTP Cloud

## Build/Test/Lint Commands
- Dev server: `npm run dev` (Note: Never run this as user already has it running at http://localhost:5173)

## Code Style Guidelines
- Use TypeScript with strict type checking
- React functional components with FC<Props> typing
- Tailwind CSS for styling with consistent class naming
- Default parameters for props when applicable
- Detailed interface definitions for component props
- ES modules (import/export)
- Consistent component structure: props definition → destructuring → rendering

## Important Rules
- Follow user instructions strictly
- Read all relevant documents before starting work
- Stop after completing each task
- Use `git add .` before committing
- Commit after finishing each task
- Take responsibility for code quality and results