{"name": "otp-manager", "private": true, "version": "0.1.0-alpha.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@marsidev/react-turnstile": "^1.1.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.7", "@types/react-helmet": "^6.1.11", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "flag-icons": "^7.3.2", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^7.2.0", "lucide-react": "^0.344.0", "otpauth": "^9.2.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-i18next": "^14.0.5", "react-icons": "^5.5.0", "react-router-dom": "^6.22.2", "tailwind-merge": "^3.3.1", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "packageManager": "npm@11.2.0+sha512.3dc9c50ba813a3d54393155a435fe66404b72685ab0e3008f9ae9ed8d81f6104860f07ed2656dd5748c1322d95f3140fa9b19c59a6bba7750fd12285f81866da"}