{"title": "Chiffrement", "setup": "Configuration du chiffrement", "setupDesc": "Créez un mot de passe secondaire pour chiffrer vos secrets OTP. Ce mot de passe ne sera jamais envoyé au serveur.", "unlock": "Déverrouiller", "unlockDesc": "Entrez votre mot de passe secondaire pour accéder à vos secrets OTP chiffrés.", "secondaryPassword": "Mot de passe secondaire", "confirmPassword": "Confirmer le mot de passe", "setupEncryption": "Configurer le chiffrement", "recoveryKey": "Clé de récupération", "saveRecoveryKey": "Sauvegardez votre clé de récupération", "saveRecoveryKeyDesc": "Voici votre clé de récupération. Conservez-la dans un endroit sûr. Vous en aurez besoin si vous oubliez votre mot de passe secondaire.", "iHaveSavedIt": "Je l'ai sauvegardée", "passwordsDoNotMatch": "Les mots de passe ne correspondent pas", "incorrectPassword": "Mot de passe incorrect", "notAuthenticated": "Vous devez être connecté pour configurer le chiffrement", "setupFailed": "Échec de la configuration du chiffrement", "resetFailed": "Échec de la réinitialisation du mot de passe", "notUnlocked": "Le chiffrement n'est pas déverrouillé", "failed": "Échec du chiffrement", "decryptionFailed": "Échec du déchiffrement des données", "sessionExpiring": "Votre session va <PERSON><PERSON><PERSON><PERSON> expirer", "sessionExpiringDesc": "Votre session de chiffrement expirera dans {{time}}. Voulez-vous la prolonger ?", "extend": "Prolonger la session", "lock": "Verrouiller maintenant", "resetPassword": "Réinitialiser le mot de passe secondaire", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmNewPassword": "Confirmer le nouveau mot de passe", "useRecoveryKey": "Utiliser la clé de récupération", "enterRecoveryKey": "Entrez la clé de récupération", "invalidRecoveryKey": "Clé de récupération invalide", "requirements": "Exigences", "passwordRequirements": "Le mot de passe ne répond pas aux exigences de sécurité", "passwordMustMeet": "Le mot de passe doit répondre à ces exigences", "minLength": "Au moins {{length}} caractères", "requireUppercase": "Au moins une lettre majuscule", "requireLowercase": "Au moins une lettre minuscule", "requireNumbers": "Au moins un chiffre", "requireSpecial": "Au moins un caractère spécial", "setupRequiredForOTP": "<PERSON><PERSON> <PERSON> configurer le chiffrement avant d'ajouter des codes OTP", "unlockRequiredForOTP": "V<PERSON> de<PERSON> dé<PERSON><PERSON>r le chiffrement avant d'ajouter des codes OTP", "recoveryKeyWarning": "IMPORTANT : Si vous perdez cette clé et oubliez votre mot de passe, vous ne pourrez plus accéder à vos données chiffrées.", "confirmSaved": "J'ai sauvegardé ma clé de récupération dans un endroit sécurisé", "storeSecurely": "Conservez cette clé dans un gestionnaire de mots de passe sécurisé ou un coffre-fort", "doNotShare": "Ne partagez jamais cette clé avec qui que ce soit", "printAndStore": "Envisagez d'imprimer cette clé et de la conserver dans un endroit sécurisé", "useToRecover": "Utilisez cette clé pour récupérer votre compte si vous oubliez votre mot de passe", "tableNotExists": "Configuration de la base de données requise. Veuillez exécuter les migrations de base de données avant d'utiliser le chiffrement.", "dbStep1": "Accédez au tableau de bord de votre projet Supabase", "dbStep2": "Ouvrez l'éditeur SQL", "dbStep3": "Exécutez les commandes SQL ci-dessous pour configurer les tables de chiffrement", "sqlToRun": "SQL à exécuter"}