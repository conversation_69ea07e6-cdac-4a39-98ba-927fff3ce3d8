import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import PageHeader from '../../components/public/PageHeader';
import { changelogService, ChangelogEntry } from '../../services/changelogService';
import FeatureStatusIndicator from '../../components/public/FeatureStatusIndicator';

const ChangelogPage: React.FC = () => {
  const { t } = useTranslation(['changelog', 'public']);
  const [changelogEntries, setChangelogEntries] = useState<ChangelogEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadChangelogEntries();
  }, []);

  const loadChangelogEntries = async () => {
    try {
      const entries = await changelogService.getPublishedEntries();
      setChangelogEntries(entries);
    } catch (error) {
      console.error('Error loading changelog entries:', error);
      // Fallback to static entries if service fails
      setChangelogEntries([
        {
          version: "0.1.0-alpha.1",
          date: "2024-12-01",
          title: "First Alpha Version",
          description: "Initial alpha release with core OTP management functionality.",
          status: 'published',
          changes: [
            'Complete authentication system with Supabase',
            'Zero-knowledge client-side encryption',
            'Add, view, and manage TOTP/HOTP codes',
            'Secure backup and recovery system',
            'Multi-language support',
            'Responsive design with modern UI'
          ]
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <PageHeader
        title={t('changelog:title')}
        subtitle={t('changelog:subtitle')}
      />

      <div className="max-w-4xl mx-auto px-4 pb-16 sm:px-6 lg:px-8">
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute top-0 bottom-0 left-8 w-0.5 bg-primary-100"></div>

          {changelogEntries.map((entry, index) => (
            <div key={entry.version} className="relative mb-12">
              {/* Version dot */}
              <div className="absolute left-8 -ml-3 h-6 w-6 rounded-full border-4 border-white bg-primary-500 shadow-md transform -translate-y-1/2"></div>

              <div className="ml-16">
                <div className="flex items-center">
                  <h3 className="text-xl font-bold text-primary-700">{entry.version}</h3>
                  <span className="ml-4 px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-700">
                    {entry.date}
                  </span>
                </div>

                <h4 className="mt-2 text-lg font-semibold text-dark-800">{entry.title}</h4>
                <p className="mt-1 text-dark-600">{entry.description}</p>

                <div className="mt-4 bg-light-50 rounded-lg p-4">
                  <h5 className="font-medium text-dark-700 mb-3">{t('changelog:changes')}:</h5>
                  <ul className="space-y-2">
                    {entry.changes.map((change, changeIndex) => (
                      <li key={changeIndex} className="flex items-start">
                        <div className="flex-shrink-0 h-5 w-5 rounded-full bg-primary-100 flex items-center justify-center mt-0.5">
                          <div className="h-2 w-2 rounded-full bg-primary-500"></div>
                        </div>
                        <span className="ml-3 text-dark-600">{change}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}

          {/* Development Note */}
          <div className="relative mb-8">
            <div className="absolute left-8 -ml-3 h-6 w-6 rounded-full border-4 border-white bg-amber-400 shadow-md transform -translate-y-1/2"></div>
            <div className="ml-16">
              <div className="inline-flex items-center">
                <h3 className="text-xl font-bold text-amber-700">{t('changelog:developmentNote')}</h3>
              </div>
              <div className="mt-4 bg-amber-50 rounded-lg p-4 border border-amber-100">
                <p className="text-dark-700 mb-4">
                  {t('changelog:developmentNoteText1')}
                </p>
                <p className="text-dark-700">
                  {t('changelog:developmentNoteText2')}
                </p>
              </div>
            </div>
          </div>

          {/* Coming soon */}
          <div className="relative mb-8">
            <div className="absolute left-8 -ml-3 h-6 w-6 rounded-full border-4 border-white bg-secondary-300 shadow-md transform -translate-y-1/2"></div>
            <div className="ml-16">
              <div className="inline-flex items-center">
                <h3 className="text-xl font-bold text-secondary-700">{t('changelog:comingSoon')}</h3>
              </div>
              <div className="mt-4 bg-light-50 rounded-lg p-4">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 rounded-full bg-secondary-100 flex items-center justify-center mt-0.5">
                      <div className="h-2 w-2 rounded-full bg-secondary-500"></div>
                    </div>
                    <span className="ml-3 text-dark-600">{t('changelog:featureShareOTP')}</span>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 rounded-full bg-secondary-100 flex items-center justify-center mt-0.5">
                      <div className="h-2 w-2 rounded-full bg-secondary-500"></div>
                    </div>
                    <span className="ml-3 text-dark-600">{t('changelog:featureBrowserExtension')}</span>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 rounded-full bg-secondary-100 flex items-center justify-center mt-0.5">
                      <div className="h-2 w-2 rounded-full bg-secondary-500"></div>
                    </div>
                    <span className="ml-3 text-dark-600">{t('changelog:featureMobileApp')}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangelogPage;