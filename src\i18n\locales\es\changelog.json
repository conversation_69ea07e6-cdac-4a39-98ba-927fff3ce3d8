{"title": "Registro de Cambios", "subtitle": "Sigue nuestras actualizaciones y mejoras del producto", "changes": "Cambios", "comingSoon": "Próximamente", "developmentNote": "Nota de Desarrollo", "developmentNoteText1": "Este proyecto está en desarrollo activo. Pueden ocurrir cambios significativos antes de la primera versión estable.", "developmentNoteText2": "La versión actual es pre-alfa y aún no está lista para uso en producción.", "firstAlphaVersion": "Primera Versión Alfa", "firstAlphaDescription": "Primera versión alfa de SecureOTP con características básicas para la gestión de códigos OTP.", "upcomingRelease": "Próximo <PERSON>", "featureShareOTP": "Compartición segura de OTP con miembros del equipo", "featureBrowserExtension": "Extensión para navegador para fácil acceso", "featureMobileApp": "Aplicación móvil para iOS y Android", "changeEntries": {"authentication": {"title": "Autenticación", "items": ["Sistema de autenticación completo con Firebase", "Creación de cuenta de usuario con correo electrónico/contraseña", "Inicio de sesión con correo electrónico/contraseña", "Autenticación social (Google, Facebook, Twitter, GitHub)", "Cierre de sesión seguro", "Protección de rutas para usuarios autenticados"]}, "otpManagement": {"title": "Gestión de OTP", "items": ["Añadir códigos OTP (TOTP y HOTP)", "Mostrar códigos OTP con temporizador para TOTP", "Eliminar códigos OTP con confirmación", "Listar códigos OTP con búsqueda y filtrado", "Vista detallada de códigos OTP", "Copiar códigos OTP al portapapeles"]}, "userInterface": {"title": "Interfaz de Usuario", "items": ["Diseño responsivo para móvil y escritorio", "Tema oscuro y claro", "Interfaz de usuario moderna con Tailwind CSS", "Componentes reutilizables para consistencia", "Animaciones y transiciones suaves", "Iconos y gráficos personalizados"]}, "security": {"title": "Seguridad", "items": ["Cifrado de extremo a extremo para datos OTP", "Almacenamiento seguro de claves de cifrado", "Protección contra ataques de fuerza bruta", "Tiempos de espera de sesión configurables", "Validación de entrada para prevenir inyecciones", "Auditoría de seguridad y pruebas de penetración"]}, "performance": {"title": "Rendimiento", "items": ["Carga de página optimizada", "Carga diferida de componentes", "Caché de datos para acceso rápido", "Optimización de imágenes y activos", "Minimización de JavaScript y CSS", "Mejoras de rendimiento del servidor"]}, "infrastructure": {"title": "Infraestructura", "items": ["Configuración de CI/CD para implementación automática", "Pruebas automatizadas para componentes críticos", "Monitoreo y registro para diagnóstico", "Escalabilidad para manejar aumento de usuarios", "Copias de seguridad regulares de datos", "Recuperación ante desastres y planes de contingencia"]}}, "futureReleases": {"title": "Próximas Versiones", "items": ["Aplicaciones nativas para iOS y Android", "Extensiones para navegadores Chrome, Firefox y Safari", "Aplicaciones de escritorio para Windows, macOS y Linux", "Compartición segura de OTP con miembros del equipo", "Integración con gestores de contraseñas populares", "Autenticación biométrica para aplicaciones móviles", "Sincronización sin conexión y modo fuera de línea", "Importación/exportación de datos cifrados", "Temas personalizables y opciones de marca", "API para desarrolladores e integraciones"]}, "betaProgram": {"title": "Programa Beta", "description": "Únete a nuestro programa beta para probar nuevas características antes de su lanzamiento público y ayudarnos a mejorar SecureOTP.", "joinButton": "Unirse al Programa Beta", "benefits": ["Acceso anticipado a nuevas características", "Influencia en el desarrollo del producto", "Soporte prioritario del equipo de desarrollo", "Reconocimiento en nuestro sitio web y documentación"]}}