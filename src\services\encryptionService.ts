/**
 * Service de chiffrement utilisant l'API Web Crypto pour des opérations cryptographiques de niveau militaire.
 * Toutes les opérations sont effectuées côté client, aucune clé ou donnée déchiffrée n'est envoyée au serveur.
 */

// Constantes pour les paramètres de chiffrement
const PBKDF2_ITERATIONS = 100000;
const PBKDF2_HASH = 'SHA-256';
const AES_KEY_LENGTH = 256;
const SESSION_TIMEOUT = 15 * 60 * 1000; // 15 minutes en millisecondes

/**
 * Dérive une clé cryptographique à partir d'un mot de passe et d'un sel
 * Utilise PBKDF2 avec 100 000 itérations pour une sécurité maximale
 */
export async function deriveKey(password: string, salt: Uint8Array): Promise<CryptoKey> {
  // Encoder le mot de passe en ArrayBuffer
  const encoder = new TextEncoder();
  const passwordBuffer = encoder.encode(password);
  
  // Importer le mot de passe comme clé brute
  const importedKey = await window.crypto.subtle.importKey(
    'raw',
    passwordBuffer,
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );
  
  // Dériver une clé AES-GCM à partir du mot de passe et du sel
  return window.crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: PBKDF2_ITERATIONS,
      hash: PBKDF2_HASH
    },
    importedKey,
    { name: 'AES-GCM', length: AES_KEY_LENGTH },
    false, // Non-extractible pour plus de sécurité
    ['encrypt', 'decrypt']
  );
}

/**
 * Chiffre des données avec une clé AES-GCM
 * Génère un vecteur d'initialisation (IV) aléatoire pour chaque opération
 */
export async function encrypt(data: string, key: CryptoKey): Promise<{ ciphertext: string, iv: string }> {
  // Encoder les données en ArrayBuffer
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  
  // Générer un IV aléatoire
  const iv = window.crypto.getRandomValues(new Uint8Array(12));
  
  // Chiffrer les données
  const encryptedBuffer = await window.crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv
    },
    key,
    dataBuffer
  );
  
  // Convertir les résultats en Base64 pour le stockage
  return {
    ciphertext: arrayBufferToBase64(encryptedBuffer),
    iv: arrayBufferToBase64(iv)
  };
}

/**
 * Déchiffre des données avec une clé AES-GCM
 */
export async function decrypt(ciphertext: string, iv: string, key: CryptoKey): Promise<string> {
  // Convertir les données de Base64 en ArrayBuffer
  const encryptedBuffer = base64ToArrayBuffer(ciphertext);
  const ivBuffer = base64ToArrayBuffer(iv);
  
  // Déchiffrer les données
  const decryptedBuffer = await window.crypto.subtle.decrypt(
    {
      name: 'AES-GCM',
      iv: ivBuffer
    },
    key,
    encryptedBuffer
  );
  
  // Décoder le résultat en chaîne de caractères
  const decoder = new TextDecoder();
  return decoder.decode(decryptedBuffer);
}

/**
 * Génère une clé aléatoire pour le chiffrement
 */
export function generateRandomKey(): Uint8Array {
  return window.crypto.getRandomValues(new Uint8Array(32));
}

/**
 * Génère une clé de récupération aléatoire sous forme de chaîne lisible
 * Format: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
 */
export function generateRecoveryKey(): string {
  const bytes = window.crypto.getRandomValues(new Uint8Array(32));
  const hex = Array.from(bytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
  
  // Formater en groupes de 4 caractères pour une meilleure lisibilité
  return hex.match(/.{1,4}/g)?.join('-') || '';
}

/**
 * Importe une clé de récupération au format chaîne vers une CryptoKey
 */
export async function importRecoveryKey(recoveryKey: string): Promise<CryptoKey> {
  // Nettoyer la clé (enlever les tirets)
  const cleanKey = recoveryKey.replace(/-/g, '');
  
  // Convertir la clé hexadécimale en ArrayBuffer
  const keyBytes = new Uint8Array(cleanKey.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16)));
  
  // Importer comme clé AES-GCM
  return window.crypto.subtle.importKey(
    'raw',
    keyBytes,
    { name: 'AES-GCM', length: AES_KEY_LENGTH },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Convertit un ArrayBuffer en chaîne Base64
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

/**
 * Convertit une chaîne Base64 en Uint8Array
 */
export function base64ToArrayBuffer(base64: string): Uint8Array {
  const binaryString = window.atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * Génère un sel aléatoire pour la dérivation de clé
 */
export function generateSalt(): Uint8Array {
  return window.crypto.getRandomValues(new Uint8Array(16));
}

/**
 * Calcule le temps restant avant expiration de la session
 */
export function getSessionTimeRemaining(sessionStartTime: number): number {
  const elapsed = Date.now() - sessionStartTime;
  return Math.max(0, SESSION_TIMEOUT - elapsed);
}

/**
 * Vérifie si la session est expirée
 */
export function isSessionExpired(sessionStartTime: number): boolean {
  return getSessionTimeRemaining(sessionStartTime) <= 0;
}

/**
 * Calcule un temps aléatoire pour la prochaine demande de mot de passe
 * Entre 10 et 20 minutes
 */
export function getRandomSessionTimeout(): number {
  // Entre 10 et 20 minutes en millisecondes
  return 10 * 60 * 1000 + Math.floor(Math.random() * 10 * 60 * 1000);
}
