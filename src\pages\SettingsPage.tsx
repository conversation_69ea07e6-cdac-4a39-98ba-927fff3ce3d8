import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Settings, User, Bell, Globe, Shield, Key, Lock,
  ChevronRight, ArrowLeft, Loader2, Save, Trash2
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useEncryption } from '../contexts/EncryptionContext';
import { useToast } from '../contexts/ToastContext';
import { supabase } from '../lib/supabase';
import SEO from '../components/SEO';
import DeleteConfirmationModal from '../components/ui/DeleteConfirmationModal';
import { languages } from '../i18n/languageConfig';

type SettingsSection = 'profile' | 'notifications' | 'language' | 'security' | 'encryption' | 'danger';

const SettingsPage: React.FC = () => {
  const { t, i18n } = useTranslation(['settings', 'profile', 'encryption', 'common']);
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isEncryptionSetup } = useEncryption();
  const { showToast } = useToast();
  const [activeSection, setActiveSection] = useState<SettingsSection>('profile');
  const [loading, setLoading] = useState(false);
  const [firstName, setFirstName] = useState(user?.user_metadata?.first_name || '');
  const [language, setLanguage] = useState(i18n.language);
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    securityAlerts: true,
    marketingEmails: false
  });
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handleSaveProfile = async () => {
    setLoading(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      // Mettre à jour les métadonnées de l'utilisateur dans Supabase
      const { error } = await supabase.auth.updateUser({
        data: { first_name: firstName }
      });

      if (error) throw error;

      setSuccessMessage(t('settings:profileSaved'));
    } catch (error) {
      setErrorMessage(t('settings:errorSaving'));
      console.error('Error saving profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeLanguage = (lang: string) => {
    setLanguage(lang);
    i18n.changeLanguage(lang);
    setSuccessMessage(t('settings:languageChanged'));
  };

  const handleSaveNotifications = async () => {
    setLoading(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      // Simuler une sauvegarde
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccessMessage(t('settings:notificationsSaved'));
    } catch (error) {
      setErrorMessage(t('settings:errorSaving'));
      console.error('Error saving notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = () => {
    // Logique de suppression de compte
    showToast(t('settings:accountDeletionInitiated'), 'info');
    // Implémenter la logique réelle de suppression ici
  };

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'profile':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <User className="mr-3 h-6 w-6 text-primary-500" />
              {t('settings:profileSettings')}
            </h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  {t('profile:email')}
                </label>
                <input
                  type="email"
                  value={user?.email || ''}
                  disabled
                  className="w-full rounded-lg border border-dark-700 bg-dark-900/50 text-white px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-70"
                />
                <p className="mt-1 text-xs text-primary-400">
                  {t('settings:emailCannotBeChanged')}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  {t('profile:firstName')}
                </label>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full rounded-lg border border-dark-700 bg-dark-900 text-white px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder={t('settings:enterFirstName')}
                />
              </div>

              {successMessage && (
                <div className="bg-green-900/30 text-green-400 p-3 rounded-lg text-sm border border-green-800">
                  {successMessage}
                </div>
              )}

              {errorMessage && (
                <div className="bg-red-900/30 text-red-400 p-3 rounded-lg text-sm border border-red-800">
                  {errorMessage}
                </div>
              )}

              <div>
                <button
                  onClick={handleSaveProfile}
                  disabled={loading}
                  className="flex items-center justify-center px-4 py-2 bg-primary-500 hover:bg-primary-400 text-dark-900 font-medium rounded-lg transition-colors disabled:opacity-70"
                >
                  {loading ? (
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-5 w-5 mr-2" />
                  )}
                  {t('settings:saveChanges')}
                </button>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Bell className="mr-3 h-6 w-6 text-primary-500" />
              {t('settings:notificationSettings')}
            </h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-dark-800 rounded-lg border border-dark-700">
                <div>
                  <h3 className="text-white font-medium">{t('settings:emailNotifications')}</h3>
                  <p className="text-sm text-primary-400">{t('settings:emailNotificationsDesc')}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationSettings.emailNotifications}
                    onChange={() => setNotificationSettings({
                      ...notificationSettings,
                      emailNotifications: !notificationSettings.emailNotifications
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-dark-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-dark-800 rounded-lg border border-dark-700">
                <div>
                  <h3 className="text-white font-medium">{t('settings:securityAlerts')}</h3>
                  <p className="text-sm text-primary-400">{t('settings:securityAlertsDesc')}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationSettings.securityAlerts}
                    onChange={() => setNotificationSettings({
                      ...notificationSettings,
                      securityAlerts: !notificationSettings.securityAlerts
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-dark-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-dark-800 rounded-lg border border-dark-700">
                <div>
                  <h3 className="text-white font-medium">{t('settings:marketingEmails')}</h3>
                  <p className="text-sm text-primary-400">{t('settings:marketingEmailsDesc')}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationSettings.marketingEmails}
                    onChange={() => setNotificationSettings({
                      ...notificationSettings,
                      marketingEmails: !notificationSettings.marketingEmails
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-dark-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>

              {successMessage && (
                <div className="bg-green-900/30 text-green-400 p-3 rounded-lg text-sm border border-green-800">
                  {successMessage}
                </div>
              )}

              {errorMessage && (
                <div className="bg-red-900/30 text-red-400 p-3 rounded-lg text-sm border border-red-800">
                  {errorMessage}
                </div>
              )}

              <div>
                <button
                  onClick={handleSaveNotifications}
                  disabled={loading}
                  className="flex items-center justify-center px-4 py-2 bg-primary-500 hover:bg-primary-400 text-dark-900 font-medium rounded-lg transition-colors disabled:opacity-70"
                >
                  {loading ? (
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-5 w-5 mr-2" />
                  )}
                  {t('settings:saveChanges')}
                </button>
              </div>
            </div>
          </div>
        );

      case 'language':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Globe className="mr-3 h-6 w-6 text-primary-500" />
              {t('settings:languageSettings')}
            </h2>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {languages.map((lang) => (
                  <div
                    key={lang.code}
                    className={`p-4 rounded-lg border cursor-pointer transition-all ${
                      language === lang.code
                        ? 'bg-primary-900/30 border-primary-500'
                        : 'bg-dark-800 border-dark-700 hover:border-primary-700'
                    }`}
                    onClick={() => handleChangeLanguage(lang.code)}
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mr-3 overflow-hidden border-2 border-dark-700">
                        <span className={`fi fi-${lang.countryCode} w-full h-full`}></span>
                      </div>
                      <div>
                        <h3 className="text-white font-medium">{lang.name}</h3>
                        <p className="text-sm text-primary-400">{lang.nativeName}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {successMessage && (
                <div className="bg-green-900/30 text-green-400 p-3 rounded-lg text-sm border border-green-800">
                  {successMessage}
                </div>
              )}
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Shield className="mr-3 h-6 w-6 text-primary-500" />
              {t('settings:securitySettings')}
            </h2>

            <div className="space-y-4">
              <div
                className="p-4 bg-dark-800 rounded-lg border border-dark-700 flex items-center justify-between cursor-pointer hover:border-primary-700 transition-all"
                onClick={() => navigate('/app/change-password')}
              >
                <div className="flex items-center">
                  <Lock className="h-5 w-5 text-primary-500 mr-3" />
                  <div>
                    <h3 className="text-white font-medium">{t('settings:changePassword')}</h3>
                    <p className="text-sm text-primary-400">{t('settings:changePasswordDesc')}</p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-primary-500" />
              </div>

              <div
                className="p-4 bg-dark-800 rounded-lg border border-dark-700 flex items-center justify-between cursor-pointer hover:border-primary-700 transition-all"
                onClick={() => navigate('/app/sessions')}
              >
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-primary-500 mr-3" />
                  <div>
                    <h3 className="text-white font-medium">{t('settings:activeSessions')}</h3>
                    <p className="text-sm text-primary-400">{t('settings:activeSessionsDesc')}</p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-primary-500" />
              </div>

              <div
                className="p-4 bg-dark-800 rounded-lg border border-dark-700 flex items-center justify-between cursor-pointer hover:border-primary-700 transition-all"
                onClick={() => navigate('/app/two-factor')}
              >
                <div className="flex items-center">
                  <Key className="h-5 w-5 text-primary-500 mr-3" />
                  <div>
                    <h3 className="text-white font-medium">{t('settings:twoFactorAuth')}</h3>
                    <p className="text-sm text-primary-400">{t('settings:twoFactorAuthDesc')}</p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-primary-500" />
              </div>
            </div>
          </div>
        );

      case 'encryption':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Lock className="mr-3 h-6 w-6 text-primary-500" />
              {t('settings:encryptionSettings')}
            </h2>

            <div className="space-y-4">
              <div className="p-4 bg-dark-800 rounded-lg border border-dark-700">
                <div className="flex items-center mb-3">
                  <Shield className="h-5 w-5 text-primary-500 mr-3" />
                  <h3 className="text-white font-medium">{t('settings:encryptionStatus')}</h3>
                </div>

                {isEncryptionSetup ? (
                  <div className="bg-green-900/30 text-green-400 p-3 rounded-lg text-sm border border-green-800">
                    {t('settings:encryptionEnabled')}
                  </div>
                ) : (
                  <div className="bg-red-900/30 text-red-400 p-3 rounded-lg text-sm border border-red-800">
                    {t('settings:encryptionDisabled')}
                  </div>
                )}

                <p className="mt-3 text-sm text-primary-400">
                  {t('settings:encryptionExplanation')}
                </p>
              </div>

              <div
                className="p-4 bg-dark-800 rounded-lg border border-dark-700 flex items-center justify-between cursor-pointer hover:border-primary-700 transition-all"
                onClick={() => navigate('/app/unlock')}
              >
                <div className="flex items-center">
                  <Key className="h-5 w-5 text-primary-500 mr-3" />
                  <div>
                    <h3 className="text-white font-medium">
                      {isEncryptionSetup
                        ? t('settings:manageEncryption')
                        : t('settings:setupEncryption')}
                    </h3>
                    <p className="text-sm text-primary-400">
                      {isEncryptionSetup
                        ? t('settings:manageEncryptionDesc')
                        : t('settings:setupEncryptionDesc')}
                    </p>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-primary-500" />
              </div>

              {isEncryptionSetup && (
                <>
                  <div
                    className="p-4 bg-dark-800 rounded-lg border border-dark-700 flex items-center justify-between cursor-pointer hover:border-primary-700 transition-all"
                    onClick={() => navigate('/app/recovery-key')}
                  >
                    <div className="flex items-center">
                      <Key className="h-5 w-5 text-primary-500 mr-3" />
                      <div>
                        <h3 className="text-white font-medium">{t('settings:recoveryKey')}</h3>
                        <p className="text-sm text-primary-400">{t('settings:recoveryKeyDesc')}</p>
                      </div>
                    </div>
                    <ChevronRight className="h-5 w-5 text-primary-500" />
                  </div>

                  <div
                    className="p-4 bg-dark-800 rounded-lg border border-dark-700 flex items-center justify-between cursor-pointer hover:border-primary-700 transition-all"
                    onClick={() => navigate('/app/settings/backup')}
                  >
                    <div className="flex items-center">
                      <Save className="h-5 w-5 text-primary-500 mr-3" />
                      <div>
                        <h3 className="text-white font-medium">{t('backup.title', { ns: 'settings' })}</h3>
                        <p className="text-sm text-primary-400">{t('backup.description', { ns: 'settings' })}</p>
                      </div>
                    </div>
                    <ChevronRight className="h-5 w-5 text-primary-500" />
                  </div>
                </>
              )}
            </div>
          </div>
        );

      case 'danger':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-red-400 flex items-center">
              <Trash2 className="mr-3 h-6 w-6 text-red-500" />
              {t('settings:dangerZone')}
            </h2>

            <div className="p-4 bg-red-900/20 rounded-lg border border-red-900/50">
              <h3 className="text-xl font-semibold text-red-400 mb-2">
                {t('settings:deleteAccount')}
              </h3>
              <p className="text-red-300 mb-4">
                {t('settings:deleteAccountWarning')}
              </p>

              <button
                className="flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-500 text-white font-medium rounded-lg transition-colors"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <Trash2 className="h-5 w-5 mr-2" />
                {t('settings:deleteAccount')}
              </button>
            </div>

            {/* Modal de confirmation de suppression */}
            <DeleteConfirmationModal
              isOpen={isDeleteModalOpen}
              onClose={() => setIsDeleteModalOpen(false)}
              onConfirm={handleDeleteAccount}
              itemName={t('settings:yourAccount')}
              itemType={t('settings:account')}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <SEO title={t('settings:title')} />
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate(-1)}
            className="mr-4 p-2 rounded-full hover:bg-dark-700 transition-colors"
            aria-label={t('common:back')}
          >
            <ArrowLeft className="h-5 w-5 text-primary-400" />
          </button>
          <h1 className="text-3xl font-bold text-white flex items-center">
            <Settings className="mr-3 h-8 w-8 text-primary-500" />
            {t('settings:title')}
          </h1>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar de navigation */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-dark-800 rounded-xl border border-dark-700 overflow-hidden">
              <nav className="flex flex-col">
                <button
                  onClick={() => setActiveSection('profile')}
                  className={`flex items-center px-4 py-3 text-left ${
                    activeSection === 'profile'
                      ? 'bg-primary-900/30 border-l-4 border-primary-500'
                      : 'hover:bg-dark-700'
                  }`}
                >
                  <User className={`h-5 w-5 mr-3 ${
                    activeSection === 'profile' ? 'text-primary-500' : 'text-primary-400'
                  }`} />
                  <span className={activeSection === 'profile' ? 'text-white font-medium' : 'text-primary-300'}>
                    {t('settings:profileSettings')}
                  </span>
                </button>

                <button
                  onClick={() => setActiveSection('notifications')}
                  className={`flex items-center px-4 py-3 text-left ${
                    activeSection === 'notifications'
                      ? 'bg-primary-900/30 border-l-4 border-primary-500'
                      : 'hover:bg-dark-700'
                  }`}
                >
                  <Bell className={`h-5 w-5 mr-3 ${
                    activeSection === 'notifications' ? 'text-primary-500' : 'text-primary-400'
                  }`} />
                  <span className={activeSection === 'notifications' ? 'text-white font-medium' : 'text-primary-300'}>
                    {t('settings:notificationSettings')}
                  </span>
                </button>

                <button
                  onClick={() => setActiveSection('language')}
                  className={`flex items-center px-4 py-3 text-left ${
                    activeSection === 'language'
                      ? 'bg-primary-900/30 border-l-4 border-primary-500'
                      : 'hover:bg-dark-700'
                  }`}
                >
                  <Globe className={`h-5 w-5 mr-3 ${
                    activeSection === 'language' ? 'text-primary-500' : 'text-primary-400'
                  }`} />
                  <span className={activeSection === 'language' ? 'text-white font-medium' : 'text-primary-300'}>
                    {t('settings:languageSettings')}
                  </span>
                </button>

                <button
                  onClick={() => setActiveSection('security')}
                  className={`flex items-center px-4 py-3 text-left ${
                    activeSection === 'security'
                      ? 'bg-primary-900/30 border-l-4 border-primary-500'
                      : 'hover:bg-dark-700'
                  }`}
                >
                  <Shield className={`h-5 w-5 mr-3 ${
                    activeSection === 'security' ? 'text-primary-500' : 'text-primary-400'
                  }`} />
                  <span className={activeSection === 'security' ? 'text-white font-medium' : 'text-primary-300'}>
                    {t('settings:securitySettings')}
                  </span>
                </button>

                <button
                  onClick={() => setActiveSection('encryption')}
                  className={`flex items-center px-4 py-3 text-left ${
                    activeSection === 'encryption'
                      ? 'bg-primary-900/30 border-l-4 border-primary-500'
                      : 'hover:bg-dark-700'
                  }`}
                >
                  <Lock className={`h-5 w-5 mr-3 ${
                    activeSection === 'encryption' ? 'text-primary-500' : 'text-primary-400'
                  }`} />
                  <span className={activeSection === 'encryption' ? 'text-white font-medium' : 'text-primary-300'}>
                    {t('settings:encryptionSettings')}
                  </span>
                </button>

                <button
                  onClick={() => setActiveSection('danger')}
                  className={`flex items-center px-4 py-3 text-left ${
                    activeSection === 'danger'
                      ? 'bg-red-900/30 border-l-4 border-red-500'
                      : 'hover:bg-dark-700'
                  }`}
                >
                  <Trash2 className={`h-5 w-5 mr-3 ${
                    activeSection === 'danger' ? 'text-red-500' : 'text-red-400'
                  }`} />
                  <span className={activeSection === 'danger' ? 'text-red-400 font-medium' : 'text-red-300'}>
                    {t('settings:dangerZone')}
                  </span>
                </button>
              </nav>
            </div>
          </div>

          {/* Contenu principal */}
          <div className="flex-1">
            <div className="bg-dark-800 rounded-xl border border-dark-700 p-6">
              {renderSectionContent()}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SettingsPage;
