import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Globe, ChevronDown } from 'lucide-react';
import { languages, getLanguageByCode } from '../i18n/languageConfig';
import 'flag-icons/css/flag-icons.min.css';

const FooterLanguageSelector: React.FC = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const currentLanguage = getLanguageByCode(i18n.language) || languages[0];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleLanguageChange = (langCode: string) => {
    i18n.changeLanguage(langCode);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="flex items-center space-x-2 px-3 py-1.5 rounded-lg text-dark-300 hover:text-white border border-dark-700 hover:border-primary-500/50 transition-all duration-200"
      >
        <Globe className="h-4 w-4" />
        <span className={`fi fi-${currentLanguage.countryCode} mr-1`}></span>
        <span className="text-sm font-medium">{currentLanguage.nativeName}</span>
        <ChevronDown className={`h-3 w-3 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute bottom-full mb-2 right-0 w-48 rounded-md shadow-lg bg-dark-800 border border-dark-700 z-50">
          <div className="py-1">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                className={`flex items-center w-full text-left px-4 py-2 text-sm transition-colors duration-200 ${
                  i18n.language === lang.code
                    ? 'bg-primary-900/30 text-primary-500'
                    : 'text-dark-300 hover:bg-dark-700 hover:text-white'
                }`}
              >
                <span className={`fi fi-${lang.countryCode} mr-2`}></span>
                <div>
                  <span className="font-medium">{lang.name}</span>
                  <span className="text-xs text-primary-500 block">{lang.nativeName}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FooterLanguageSelector;
