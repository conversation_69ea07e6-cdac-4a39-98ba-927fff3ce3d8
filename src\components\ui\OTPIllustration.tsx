import React from 'react';
import { <PERSON>, Smartphone, Lock, Key, Laptop } from 'lucide-react';
import PixelPattern from './PixelPattern';

interface OTPIllustrationProps {
  className?: string;
  animated?: boolean;
}

const OTPIllustration: React.FC<OTPIllustrationProps> = ({
  className = '',
  animated = true,
}) => {
  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Arrière-plan avec motif de pixels */}
      <div className="absolute inset-0 opacity-30">
        <PixelPattern color="secondary" size={8} density={0.2} animated={animated} />
      </div>
      
      {/* Carte OTP principale */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="relative w-64 h-96 bg-dark-800 rounded-3xl border border-dark-700 shadow-xl overflow-hidden transform rotate-12">
          {/* Motif de pixels sur la carte */}
          <div className="absolute inset-0 bg-pixel-pattern bg-[length:15px_15px] opacity-30"></div>
          
          {/* Contenu de la carte */}
          <div className="absolute inset-0 p-6 flex flex-col">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-primary-500 flex items-center justify-center shadow-neon">
                <Shield className="h-5 w-5 text-dark-900" />
              </div>
              <span className="ml-2 text-white font-bold font-mono">SecureOTP</span>
            </div>
            
            <div className="mt-8 flex flex-col items-center">
              <div className="text-3xl font-mono font-bold tracking-widest text-primary-500 animate-pulse-slow">
                123 456
              </div>
              <div className="mt-2 w-full max-w-[120px] h-1.5 bg-dark-600 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full animate-pulse-slow"
                  style={{ width: '70%' }}
                ></div>
              </div>
              <span className="mt-1 text-xs text-primary-300 font-mono">21s remaining</span>
            </div>
            
            <div className="mt-auto">
              <h3 className="text-white font-bold">My Google Account</h3>
              <p className="text-primary-300 text-sm">google.com</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Éléments flottants */}
      <div className={`absolute top-10 left-10 ${animated ? 'animate-float' : ''}`} style={{ animationDelay: '0.5s' }}>
        <div className="bg-dark-800 p-3 rounded-xl border border-dark-700 shadow-xl">
          <Smartphone className="h-8 w-8 text-primary-400" />
        </div>
      </div>
      
      <div className={`absolute bottom-20 left-5 ${animated ? 'animate-float' : ''}`} style={{ animationDelay: '1.2s' }}>
        <div className="bg-dark-800 p-3 rounded-xl border border-dark-700 shadow-xl">
          <Lock className="h-8 w-8 text-secondary-400" />
        </div>
      </div>
      
      <div className={`absolute top-20 right-10 ${animated ? 'animate-float' : ''}`} style={{ animationDelay: '0.8s' }}>
        <div className="bg-dark-800 p-3 rounded-xl border border-dark-700 shadow-xl">
          <Key className="h-8 w-8 text-primary-400" />
        </div>
      </div>
      
      <div className={`absolute bottom-10 right-10 ${animated ? 'animate-float' : ''}`} style={{ animationDelay: '1.5s' }}>
        <div className="bg-dark-800 p-3 rounded-xl border border-dark-700 shadow-xl">
          <Laptop className="h-8 w-8 text-secondary-400" />
        </div>
      </div>
      
      {/* Lignes de connexion */}
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
        <path 
          d="M100,100 C150,150 200,150 250,100" 
          fill="none" 
          stroke="url(#gradient1)" 
          strokeWidth="2" 
          strokeDasharray="5,5"
          className={animated ? 'animate-pulse-slow' : ''}
        />
        <path 
          d="M80,300 C120,250 180,250 220,300" 
          fill="none" 
          stroke="url(#gradient2)" 
          strokeWidth="2" 
          strokeDasharray="5,5"
          className={animated ? 'animate-pulse-slow' : ''}
        />
        <path 
          d="M300,120 C250,180 250,220 300,280" 
          fill="none" 
          stroke="url(#gradient1)" 
          strokeWidth="2" 
          strokeDasharray="5,5"
          className={animated ? 'animate-pulse-slow' : ''}
        />
        
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#FFEB3B" />
            <stop offset="100%" stopColor="#9C27B0" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#9C27B0" />
            <stop offset="100%" stopColor="#FFEB3B" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
};

export default OTPIllustration;
