/**
 * Enterprise Design System 2025 - TypeScript Utilities
 * Provides comprehensive utilities for working with the modern design token system
 */

// Theme Management
export type Theme = 'light' | 'dark' | 'auto';

export const themeManager = {
  /**
   * Set the theme for the application
   */
  setTheme: (theme: Theme) => {
    if (theme === 'auto') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', systemTheme);
      localStorage.removeItem('theme');
    } else {
      document.documentElement.setAttribute('data-theme', theme);
      localStorage.setItem('theme', theme);
    }
  },

  /**
   * Get the current theme
   */
  getTheme: (): Theme => {
    const stored = localStorage.getItem('theme') as Theme;
    if (stored && ['light', 'dark'].includes(stored)) return stored;

    return 'auto';
  },

  /**
   * Get the actual applied theme (resolves 'auto' to 'light' or 'dark')
   */
  getAppliedTheme: (): 'light' | 'dark' => {
    const theme = themeManager.getTheme();
    if (theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return theme;
  },

  /**
   * Toggle between light, dark, and auto themes
   */
  toggleTheme: () => {
    const current = themeManager.getTheme();
    const themes: Theme[] = ['light', 'dark', 'auto'];
    const currentIndex = themes.indexOf(current);
    const next = themes[(currentIndex + 1) % themes.length];
    themeManager.setTheme(next);
    return next;
  },

  /**
   * Initialize theme on app startup
   */
  initialize: () => {
    const theme = themeManager.getTheme();
    themeManager.setTheme(theme);

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      if (themeManager.getTheme() === 'auto') {
        document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);

    // Return cleanup function
    return () => mediaQuery.removeEventListener('change', handleChange);
  },
};

// Enhanced Design Token System
export const designTokens = {
  // Spacing Scale - 4px Grid System
  spacing: {
    0: 'var(--space-0)',
    px: 'var(--space-px)',
    0.5: 'var(--space-0-5)',
    1: 'var(--space-1)',
    1.5: 'var(--space-1-5)',
    2: 'var(--space-2)',
    2.5: 'var(--space-2-5)',
    3: 'var(--space-3)',
    3.5: 'var(--space-3-5)',
    4: 'var(--space-4)',
    5: 'var(--space-5)',
    6: 'var(--space-6)',
    7: 'var(--space-7)',
    8: 'var(--space-8)',
    9: 'var(--space-9)',
    10: 'var(--space-10)',
    11: 'var(--space-11)',
    12: 'var(--space-12)',
    14: 'var(--space-14)',
    16: 'var(--space-16)',
    20: 'var(--space-20)',
    24: 'var(--space-24)',
    28: 'var(--space-28)',
    32: 'var(--space-32)',
    36: 'var(--space-36)',
    40: 'var(--space-40)',
    44: 'var(--space-44)',
    48: 'var(--space-48)',
    52: 'var(--space-52)',
    56: 'var(--space-56)',
    60: 'var(--space-60)',
    64: 'var(--space-64)',
    72: 'var(--space-72)',
    80: 'var(--space-80)',
    96: 'var(--space-96)',
  },

  // Typography Scale - Enhanced
  fontSize: {
    '2xs': 'var(--font-size-2xs)',
    xs: 'var(--font-size-xs)',
    sm: 'var(--font-size-sm)',
    base: 'var(--font-size-base)',
    lg: 'var(--font-size-lg)',
    xl: 'var(--font-size-xl)',
    '2xl': 'var(--font-size-2xl)',
    '3xl': 'var(--font-size-3xl)',
    '4xl': 'var(--font-size-4xl)',
    '5xl': 'var(--font-size-5xl)',
    '6xl': 'var(--font-size-6xl)',
    '7xl': 'var(--font-size-7xl)',
    '8xl': 'var(--font-size-8xl)',
    '9xl': 'var(--font-size-9xl)',
  },

  // Font Weights
  fontWeight: {
    thin: 'var(--font-weight-thin)',
    extralight: 'var(--font-weight-extralight)',
    light: 'var(--font-weight-light)',
    normal: 'var(--font-weight-normal)',
    medium: 'var(--font-weight-medium)',
    semibold: 'var(--font-weight-semibold)',
    bold: 'var(--font-weight-bold)',
    extrabold: 'var(--font-weight-extrabold)',
    black: 'var(--font-weight-black)',
  },

  // Line Heights
  lineHeight: {
    none: 'var(--line-height-none)',
    tight: 'var(--line-height-tight)',
    snug: 'var(--line-height-snug)',
    normal: 'var(--line-height-normal)',
    relaxed: 'var(--line-height-relaxed)',
    loose: 'var(--line-height-loose)',
  },

  // Elevation System (Shadows)
  elevation: {
    none: 'var(--elevation-none)',
    xs: 'var(--elevation-xs)',
    sm: 'var(--elevation-sm)',
    md: 'var(--elevation-md)',
    lg: 'var(--elevation-lg)',
    xl: 'var(--elevation-xl)',
    '2xl': 'var(--elevation-2xl)',
    '3xl': 'var(--elevation-3xl)',
    inner: 'var(--elevation-inner)',
    focus: 'var(--elevation-focus)',
  },

  // Border Radius
  borderRadius: {
    none: 'var(--radius-none)',
    xs: 'var(--radius-xs)',
    sm: 'var(--radius-sm)',
    md: 'var(--radius-md)',
    lg: 'var(--radius-lg)',
    xl: 'var(--radius-xl)',
    '2xl': 'var(--radius-2xl)',
    '3xl': 'var(--radius-3xl)',
    full: 'var(--radius-full)',
  },

  // Animation Durations
  duration: {
    instant: 'var(--duration-instant)',
    fast: 'var(--duration-fast)',
    normal: 'var(--duration-normal)',
    slow: 'var(--duration-slow)',
    slower: 'var(--duration-slower)',
    slowest: 'var(--duration-slowest)',
  },

  // Easing Functions
  easing: {
    linear: 'var(--easing-linear)',
    ease: 'var(--easing-ease)',
    easeIn: 'var(--easing-ease-in)',
    easeOut: 'var(--easing-ease-out)',
    easeInOut: 'var(--easing-ease-in-out)',
    bounce: 'var(--easing-bounce)',
    spring: 'var(--easing-spring)',
  },

  // Transitions
  transitions: {
    fast: 'var(--transition-fast)',
    normal: 'var(--transition-normal)',
    slow: 'var(--transition-slow)',
    bounce: 'var(--transition-bounce)',
    spring: 'var(--transition-spring)',
  },

  // Z-Index Scale
  zIndex: {
    base: 'var(--z-base)',
    docked: 'var(--z-docked)',
    dropdown: 'var(--z-dropdown)',
    sticky: 'var(--z-sticky)',
    banner: 'var(--z-banner)',
    overlay: 'var(--z-overlay)',
    modal: 'var(--z-modal)',
    popover: 'var(--z-popover)',
    skipLink: 'var(--z-skip-link)',
    toast: 'var(--z-toast)',
    tooltip: 'var(--z-tooltip)',
  },

  // Breakpoints
  breakpoints: {
    xs: 'var(--breakpoint-xs)',
    sm: 'var(--breakpoint-sm)',
    md: 'var(--breakpoint-md)',
    lg: 'var(--breakpoint-lg)',
    xl: 'var(--breakpoint-xl)',
    '2xl': 'var(--breakpoint-2xl)',
  },

  // Legacy Aliases for Backward Compatibility
  shadows: {
    xs: 'var(--elevation-xs)',
    sm: 'var(--elevation-sm)',
    md: 'var(--elevation-md)',
    lg: 'var(--elevation-lg)',
    xl: 'var(--elevation-xl)',
    '2xl': 'var(--elevation-2xl)',
    inner: 'var(--elevation-inner)',
  },
} as const;

// Component Variants
export const componentVariants = {
  button: {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
  },
  card: {
    elevated: 'card-elevated',
    interactive: 'card-interactive',
  },
  glass: {
    light: 'glass-light',
    dark: 'glass-dark',
  },
  blur: {
    sm: 'blur-progressive-sm',
    md: 'blur-progressive-md',
    lg: 'blur-progressive-lg',
    xl: 'blur-progressive-xl',
    '2xl': 'blur-progressive-2xl',
    '3xl': 'blur-progressive-3xl',
  },
  text: {
    display1: 'text-display-1',
    display2: 'text-display-2',
    heading1: 'text-heading-1',
    heading2: 'text-heading-2',
    heading3: 'text-heading-3',
    bodyLarge: 'text-body-large',
    body: 'text-body',
    bodySmall: 'text-body-small',
    caption: 'text-caption',
  },
} as const;

// Utility Functions
export const designUtils = {
  /**
   * Combine CSS classes with proper handling of conditionals
   */
  cn: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(' ');
  },

  /**
   * Get CSS custom property value
   */
  getCSSVar: (property: string): string => {
    return getComputedStyle(document.documentElement).getPropertyValue(property);
  },

  /**
   * Set CSS custom property value
   */
  setCSSVar: (property: string, value: string): void => {
    document.documentElement.style.setProperty(property, value);
  },

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Check if user prefers high contrast
   */
  prefersHighContrast: (): boolean => {
    return window.matchMedia('(prefers-contrast: high)').matches;
  },

  /**
   * Get responsive breakpoint information
   */
  getBreakpoint: (): 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' => {
    const width = window.innerWidth;
    if (width < 640) return 'xs';
    if (width < 768) return 'sm';
    if (width < 1024) return 'md';
    if (width < 1280) return 'lg';
    if (width < 1536) return 'xl';
    return '2xl';
  },

  /**
   * Create a container query observer
   */
  createContainerObserver: (
    element: HTMLElement,
    callback: (size: { width: number; height: number }) => void
  ): ResizeObserver => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        callback({ width, height });
      }
    });

    observer.observe(element);
    return observer;
  },

  /**
   * Generate focus ring styles with enhanced accessibility
   */
  focusRing: (color = 'var(--color-border-focus)', offset = '2px') => ({
    outline: `2px solid ${color}`,
    outlineOffset: offset,
    borderRadius: 'var(--radius-md)',
  }),

  /**
   * Generate hover lift effect with customizable parameters
   */
  hoverLift: (translateY = '-2px', shadow = designTokens.elevation.md, scale = '1') => ({
    transform: `translateY(${translateY}) scale(${scale})`,
    boxShadow: shadow,
  }),

  /**
   * Create fluid spacing between breakpoints
   */
  fluidSpacing: (min: number, max: number, minVw = 320, maxVw = 1200): string => {
    const slope = (max - min) / (maxVw - minVw);
    const yAxisIntersection = -minVw * slope + min;
    return `clamp(${min}px, ${yAxisIntersection}px + ${slope * 100}vw, ${max}px)`;
  },

  /**
   * Generate responsive typography styles
   */
  responsiveType: (
    mobile: keyof typeof designTokens.fontSize,
    desktop: keyof typeof designTokens.fontSize,
    breakpoint: keyof typeof designTokens.breakpoints = 'md'
  ) => ({
    fontSize: designTokens.fontSize[mobile],
    [`@media (min-width: ${designTokens.breakpoints[breakpoint]})`]: {
      fontSize: designTokens.fontSize[desktop],
    },
  }),

  /**
   * Generate consistent component spacing
   */
  componentSpacing: (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md') => {
    const spacingMap = {
      xs: {
        padding: designTokens.spacing[2],
        gap: designTokens.spacing[1],
        margin: designTokens.spacing[1]
      },
      sm: {
        padding: designTokens.spacing[3],
        gap: designTokens.spacing[2],
        margin: designTokens.spacing[2]
      },
      md: {
        padding: designTokens.spacing[4],
        gap: designTokens.spacing[3],
        margin: designTokens.spacing[3]
      },
      lg: {
        padding: designTokens.spacing[6],
        gap: designTokens.spacing[4],
        margin: designTokens.spacing[4]
      },
      xl: {
        padding: designTokens.spacing[8],
        gap: designTokens.spacing[6],
        margin: designTokens.spacing[6]
      },
    };
    return spacingMap[size];
  },

  /**
   * Generate consistent card styles
   */
  cardStyles: (variant: 'default' | 'elevated' | 'outlined' | 'ghost' = 'default') => {
    const baseStyles = {
      borderRadius: designTokens.borderRadius.lg,
      padding: designTokens.spacing[6],
      transition: designTokens.transitions.normal,
    };

    const variants = {
      default: {
        ...baseStyles,
        backgroundColor: 'var(--color-surface-elevated)',
        border: '1px solid var(--color-border-primary)',
        boxShadow: designTokens.elevation.sm,
      },
      elevated: {
        ...baseStyles,
        backgroundColor: 'var(--color-surface-elevated)',
        border: '1px solid var(--color-border-primary)',
        boxShadow: designTokens.elevation.lg,
      },
      outlined: {
        ...baseStyles,
        backgroundColor: 'transparent',
        border: '2px solid var(--color-border-primary)',
        boxShadow: 'none',
      },
      ghost: {
        ...baseStyles,
        backgroundColor: 'transparent',
        border: 'none',
        boxShadow: 'none',
      },
    };

    return variants[variant];
  },

  /**
   * Generate button styles with variants
   */
  buttonStyles: (
    variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' = 'primary',
    size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md'
  ) => {
    const sizeMap = {
      xs: { padding: `${designTokens.spacing[1]} ${designTokens.spacing[2]}`, fontSize: designTokens.fontSize['2xs'] },
      sm: { padding: `${designTokens.spacing[2]} ${designTokens.spacing[3]}`, fontSize: designTokens.fontSize.xs },
      md: { padding: `${designTokens.spacing[2.5]} ${designTokens.spacing[4]}`, fontSize: designTokens.fontSize.sm },
      lg: { padding: `${designTokens.spacing[3]} ${designTokens.spacing[6]}`, fontSize: designTokens.fontSize.base },
      xl: { padding: `${designTokens.spacing[4]} ${designTokens.spacing[8]}`, fontSize: designTokens.fontSize.lg },
    };

    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: designTokens.spacing[2],
      borderRadius: designTokens.borderRadius.md,
      fontWeight: designTokens.fontWeight.medium,
      lineHeight: designTokens.lineHeight.none,
      cursor: 'pointer',
      transition: designTokens.transitions.normal,
      border: '1px solid transparent',
      textDecoration: 'none',
      userSelect: 'none',
      ...sizeMap[size],
    };

    const variants = {
      primary: {
        ...baseStyles,
        backgroundColor: 'var(--color-interactive-primary)',
        color: 'var(--color-text-inverse)',
        borderColor: 'var(--color-interactive-primary)',
      },
      secondary: {
        ...baseStyles,
        backgroundColor: 'var(--color-interactive-secondary)',
        color: 'var(--color-text-primary)',
        borderColor: 'var(--color-border-primary)',
      },
      outline: {
        ...baseStyles,
        backgroundColor: 'transparent',
        color: 'var(--color-text-brand)',
        borderColor: 'var(--color-border-brand)',
      },
      ghost: {
        ...baseStyles,
        backgroundColor: 'transparent',
        color: 'var(--color-text-secondary)',
        borderColor: 'transparent',
      },
      destructive: {
        ...baseStyles,
        backgroundColor: 'var(--primitive-error-600)',
        color: 'var(--color-text-inverse)',
        borderColor: 'var(--primitive-error-600)',
      },
    };

    return variants[variant];
  },
};

// Type Definitions for Design System
export type SpacingToken = keyof typeof designTokens.spacing;
export type FluidSpacingToken = keyof typeof designTokens.fluidSpacing;
export type FontSizeToken = keyof typeof designTokens.fontSize;
export type ShadowToken = keyof typeof designTokens.shadows;
export type BorderRadiusToken = keyof typeof designTokens.borderRadius;
export type BlurToken = keyof typeof designTokens.blur;
export type TransitionToken = keyof typeof designTokens.transitions;
export type ZIndexToken = keyof typeof designTokens.zIndex;

export type ButtonVariant = keyof typeof componentVariants.button;
export type CardVariant = keyof typeof componentVariants.card;
export type GlassVariant = keyof typeof componentVariants.glass;
export type BlurVariant = keyof typeof componentVariants.blur;
export type TextVariant = keyof typeof componentVariants.text;

// Export everything for easy access
export default {
  themeManager,
  designTokens,
  componentVariants,
  designUtils,
};