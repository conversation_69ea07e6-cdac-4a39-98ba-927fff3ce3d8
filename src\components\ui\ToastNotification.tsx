import React, { useEffect, useState, useRef } from 'react';
import { Check<PERSON>ircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-react';
import { designUtils } from '../../lib/designSystem';

export type ToastType = 'success' | 'error' | 'warning' | 'info';
export type ToastPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';

export interface ToastNotificationProps {
  // Core Properties
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  
  // Timing
  duration?: number; // 0 for persistent
  delay?: number;
  
  // Visual Properties
  icon?: React.ComponentType<any>;
  showIcon?: boolean;
  showProgress?: boolean;
  
  // Interaction
  dismissible?: boolean;
  onClick?: () => void;
  onDismiss?: () => void;
  
  // Progressive Enhancement
  supportOffline?: boolean;
  persistAcrossSessions?: boolean;
  
  // Accessibility
  ariaLive?: 'polite' | 'assertive';
  screenReaderOnly?: boolean;
  
  className?: string;
}

export interface ToastContainerProps {
  position?: ToastPosition;
  maxToasts?: number;
  spacing?: number;
  className?: string;
}

// Toast context for managing multiple toasts
interface ToastContextValue {
  toasts: ToastNotificationProps[];
  addToast: (toast: Omit<ToastNotificationProps, 'id'>) => string;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

const ToastContext = React.createContext<ToastContextValue | null>(null);

// Hook for using toast context
export const useToast = () => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Individual Toast Component
const ToastNotification: React.FC<ToastNotificationProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  delay = 0,
  icon: CustomIcon,
  showIcon = true,
  showProgress = true,
  dismissible = true,
  onClick,
  onDismiss,
  supportOffline = false,
  persistAcrossSessions = false,
  ariaLive = 'polite',
  screenReaderOnly = false,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [progress, setProgress] = useState(100);
  const [isPaused, setIsPaused] = useState(false);
  const timerRef = useRef<NodeJS.Timeout>();
  const progressRef = useRef<NodeJS.Timeout>();
  const toastRef = useRef<HTMLDivElement>(null);

  // Show toast with delay
  useEffect(() => {
    const showTimer = setTimeout(() => {
      setIsVisible(true);
      
      // Start auto-dismiss timer
      if (duration > 0) {
        startDismissTimer();
      }
    }, delay);

    return () => clearTimeout(showTimer);
  }, [delay, duration]);

  // Persist across sessions if enabled
  useEffect(() => {
    if (persistAcrossSessions && isVisible) {
      const toastData = {
        id,
        type,
        title,
        message,
        timestamp: Date.now(),
      };
      
      try {
        const stored = localStorage.getItem('persistedToasts');
        const toasts = stored ? JSON.parse(stored) : [];
        toasts.push(toastData);
        
        // Keep only last 10 toasts
        if (toasts.length > 10) {
          toasts.splice(0, toasts.length - 10);
        }
        
        localStorage.setItem('persistedToasts', JSON.stringify(toasts));
      } catch (error) {
        console.warn('Failed to persist toast:', error);
      }
    }
  }, [id, type, title, message, persistAcrossSessions, isVisible]);

  // Handle offline support
  useEffect(() => {
    if (!supportOffline) return;

    const handleOnline = () => {
      // Re-show toast when coming back online if it was important
      if (type === 'error' || type === 'warning') {
        setIsVisible(true);
      }
    };

    const handleOffline = () => {
      // Pause auto-dismiss when offline
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        setIsPaused(true);
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [supportOffline, type]);

  const startDismissTimer = () => {
    if (duration <= 0) return;

    // Progress animation
    if (showProgress) {
      setProgress(100);
      progressRef.current = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev - (100 / (duration / 50));
          return Math.max(0, newProgress);
        });
      }, 50);
    }

    // Dismiss timer
    timerRef.current = setTimeout(() => {
      handleDismiss();
    }, duration);
  };

  const pauseTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      setIsPaused(true);
    }
    if (progressRef.current) {
      clearInterval(progressRef.current);
    }
  };

  const resumeTimer = () => {
    if (isPaused && duration > 0) {
      setIsPaused(false);
      const remainingTime = (progress / 100) * duration;
      
      if (showProgress) {
        progressRef.current = setInterval(() => {
          setProgress(prev => {
            const newProgress = prev - (100 / (remainingTime / 50));
            return Math.max(0, newProgress);
          });
        }, 50);
      }

      timerRef.current = setTimeout(() => {
        handleDismiss();
      }, remainingTime);
    }
  };

  const handleDismiss = () => {
    if (timerRef.current) clearTimeout(timerRef.current);
    if (progressRef.current) clearInterval(progressRef.current);
    
    setIsExiting(true);
    
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, 300);
  };

  const handleClick = () => {
    onClick?.();
    if (onClick) {
      handleDismiss();
    }
  };

  // Get appropriate icon
  const getIcon = () => {
    if (CustomIcon) return CustomIcon;

    switch (type) {
      case 'success':
        return CheckCircle;
      case 'error':
        return AlertCircle;
      case 'warning':
        return AlertTriangle;
      case 'info':
        return Info;
      default:
        return Info;
    }
  };

  // Get colors based on type
  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-800',
          text: 'text-green-800 dark:text-green-200',
          icon: 'text-green-600 dark:text-green-400',
          progress: 'bg-green-500',
        };
      case 'error':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-800',
          text: 'text-red-800 dark:text-red-200',
          icon: 'text-red-600 dark:text-red-400',
          progress: 'bg-red-500',
        };
      case 'warning':
        return {
          bg: 'bg-amber-50 dark:bg-amber-900/20',
          border: 'border-amber-200 dark:border-amber-800',
          text: 'text-amber-800 dark:text-amber-200',
          icon: 'text-amber-600 dark:text-amber-400',
          progress: 'bg-amber-500',
        };
      case 'info':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          text: 'text-blue-800 dark:text-blue-200',
          icon: 'text-blue-600 dark:text-blue-400',
          progress: 'bg-blue-500',
        };
    }
  };

  if (!isVisible) return null;

  const IconComponent = getIcon();
  const colors = getColors();

  // Screen reader only version
  if (screenReaderOnly) {
    return (
      <div
        className="sr-only"
        role="alert"
        aria-live={ariaLive}
        aria-atomic="true"
      >
        {title && `${title}: `}{message}
      </div>
    );
  }

  return (
    <div
      ref={toastRef}
      className={designUtils.cn(
        'relative max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
        colors.bg,
        colors.border,
        'border',
        !isExiting && 'animate-slide-up',
        isExiting && 'animate-fade-out',
        onClick && 'cursor-pointer hover:shadow-xl transition-shadow',
        className
      )}
      onClick={handleClick}
      onMouseEnter={pauseTimer}
      onMouseLeave={resumeTimer}
      role="alert"
      aria-live={ariaLive}
      aria-atomic="true"
    >
      {/* Progress bar */}
      {showProgress && duration > 0 && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
          <div
            className={designUtils.cn('h-full transition-all duration-75 ease-linear', colors.progress)}
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      <div className="p-4">
        <div className="flex items-start">
          {/* Icon */}
          {showIcon && (
            <div className={designUtils.cn('flex-shrink-0', colors.icon)}>
              <IconComponent className="w-5 h-5" />
            </div>
          )}

          {/* Content */}
          <div className={designUtils.cn('ml-3 w-0 flex-1', !showIcon && 'ml-0')}>
            {title && (
              <p className={designUtils.cn('text-sm font-medium', colors.text)}>
                {title}
              </p>
            )}
            <p className={designUtils.cn('text-sm', colors.text, title && 'mt-1')}>
              {message}
            </p>
          </div>

          {/* Dismiss button */}
          {dismissible && (
            <div className="ml-4 flex-shrink-0 flex">
              <button
                className={designUtils.cn(
                  'rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
                  colors.text,
                  'opacity-60 hover:opacity-100'
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDismiss();
                }}
                aria-label="Dismiss notification"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Pause indicator */}
      {isPaused && (
        <div className="absolute bottom-2 right-2 text-xs opacity-60">
          Paused
        </div>
      )}
    </div>
  );
};

// Toast Container Component
export const ToastContainer: React.FC<ToastContainerProps> = ({
  position = 'top-right',
  maxToasts = 5,
  spacing = 8,
  className,
}) => {
  const { toasts, removeToast } = useToast();

  // Get position styles
  const getPositionStyles = () => {
    const styles: React.CSSProperties = {
      position: 'fixed',
      zIndex: 1080,
    };

    switch (position) {
      case 'top-right':
        styles.top = 16;
        styles.right = 16;
        break;
      case 'top-left':
        styles.top = 16;
        styles.left = 16;
        break;
      case 'bottom-right':
        styles.bottom = 16;
        styles.right = 16;
        break;
      case 'bottom-left':
        styles.bottom = 16;
        styles.left = 16;
        break;
      case 'top-center':
        styles.top = 16;
        styles.left = '50%';
        styles.transform = 'translateX(-50%)';
        break;
      case 'bottom-center':
        styles.bottom = 16;
        styles.left = '50%';
        styles.transform = 'translateX(-50%)';
        break;
    }

    return styles;
  };

  const visibleToasts = toasts.slice(0, maxToasts);

  return (
    <div
      className={designUtils.cn('pointer-events-none', className)}
      style={getPositionStyles()}
    >
      <div 
        className="space-y-2 pointer-events-auto"
        style={{ gap: spacing }}
      >
        {visibleToasts.map((toast, index) => (
          <ToastNotification
            key={toast.id}
            {...toast}
            onDismiss={() => removeToast(toast.id)}
            style={{
              animationDelay: `${index * 100}ms`,
            }}
          />
        ))}
      </div>
    </div>
  );
};

// Toast Provider Component
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastNotificationProps[]>([]);

  const addToast = (toast: Omit<ToastNotificationProps, 'id'>): string => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newToast = { ...toast, id };
    
    setToasts(prev => [...prev, newToast]);
    return id;
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearToasts = () => {
    setToasts([]);
  };

  const contextValue: ToastContextValue = {
    toasts,
    addToast,
    removeToast,
    clearToasts,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
    </ToastContext.Provider>
  );
};

export default ToastNotification;