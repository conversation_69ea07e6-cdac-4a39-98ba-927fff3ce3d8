import React, { useEffect, useState } from 'react';
import { 
  Lock, 
  Unlock, 
  Shield, 
  ShieldCheck, 
  ShieldAlert, 
  Key, 
  Eye, 
  EyeOff,
  Wifi,
  WifiOff,
  CheckCircle,
  AlertCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { designUtils } from '../../lib/designSystem';

export type EncryptionStatus = 'encrypted' | 'decrypted' | 'encrypting' | 'decrypting' | 'error';
export type SecurityLevel = 'high' | 'medium' | 'low' | 'critical';
export type BadgeVariant = 'status' | 'encryption' | 'session' | 'connection' | 'verification';

export interface SecurityBadgeProps {
  // Core Properties
  variant: BadgeVariant;
  status: EncryptionStatus | SecurityLevel | 'active' | 'inactive' | 'verified' | 'unverified' | 'connected' | 'disconnected';
  
  // Content
  label?: string;
  description?: string;
  timestamp?: Date;
  
  // Visual Properties
  size?: 'xs' | 'sm' | 'md' | 'lg';
  animated?: boolean;
  showIcon?: boolean;
  showPulse?: boolean;
  
  // Interaction Properties
  interactive?: boolean;
  onClick?: () => void;
  
  // Accessibility
  ariaLabel?: string;
  className?: string;
}

const SecurityBadge: React.FC<SecurityBadgeProps> = ({
  variant,
  status,
  label,
  description,
  timestamp,
  size = 'md',
  animated = true,
  showIcon = true,
  showPulse = false,
  interactive = false,
  onClick,
  ariaLabel,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [pulseActive, setPulseActive] = useState(showPulse);

  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(true);
    }
  }, [animated]);

  // Update pulse based on status changes
  useEffect(() => {
    if (showPulse) {
      setPulseActive(true);
      const timer = setTimeout(() => setPulseActive(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [status, showPulse]);

  // Get icon based on variant and status
  const getIcon = () => {
    switch (variant) {
      case 'encryption':
        switch (status) {
          case 'encrypted':
            return <Lock className="w-full h-full" />;
          case 'decrypted':
            return <Unlock className="w-full h-full" />;
          case 'encrypting':
          case 'decrypting':
            return <Key className="w-full h-full animate-pulse" />;
          case 'error':
            return <AlertCircle className="w-full h-full" />;
          default:
            return <Lock className="w-full h-full" />;
        }
      
      case 'status':
        switch (status) {
          case 'high':
            return <ShieldCheck className="w-full h-full" />;
          case 'medium':
            return <Shield className="w-full h-full" />;
          case 'low':
          case 'critical':
            return <ShieldAlert className="w-full h-full" />;
          default:
            return <Shield className="w-full h-full" />;
        }
      
      case 'session':
        switch (status) {
          case 'active':
            return <CheckCircle className="w-full h-full" />;
          case 'inactive':
            return <Clock className="w-full h-full" />;
          default:
            return <AlertCircle className="w-full h-full" />;
        }
      
      case 'connection':
        switch (status) {
          case 'connected':
            return <Wifi className="w-full h-full" />;
          case 'disconnected':
            return <WifiOff className="w-full h-full" />;
          default:
            return <AlertCircle className="w-full h-full" />;
        }
      
      case 'verification':
        switch (status) {
          case 'verified':
            return <CheckCircle className="w-full h-full" />;
          case 'unverified':
            return <XCircle className="w-full h-full" />;
          default:
            return <AlertCircle className="w-full h-full" />;
        }
      
      default:
        return <Shield className="w-full h-full" />;
    }
  };

  // Get colors based on status
  const getStatusColors = () => {
    // Encryption status colors
    if (variant === 'encryption') {
      switch (status) {
        case 'encrypted':
          return {
            bg: 'bg-green-50 dark:bg-green-900/20',
            border: 'border-green-200 dark:border-green-800',
            text: 'text-green-700 dark:text-green-300',
            icon: 'text-green-600 dark:text-green-400',
            dot: 'bg-green-500',
          };
        case 'decrypted':
          return {
            bg: 'bg-amber-50 dark:bg-amber-900/20',
            border: 'border-amber-200 dark:border-amber-800',
            text: 'text-amber-700 dark:text-amber-300',
            icon: 'text-amber-600 dark:text-amber-400',
            dot: 'bg-amber-500',
          };
        case 'encrypting':
        case 'decrypting':
          return {
            bg: 'bg-blue-50 dark:bg-blue-900/20',
            border: 'border-blue-200 dark:border-blue-800',
            text: 'text-blue-700 dark:text-blue-300',
            icon: 'text-blue-600 dark:text-blue-400',
            dot: 'bg-blue-500',
          };
        case 'error':
          return {
            bg: 'bg-red-50 dark:bg-red-900/20',
            border: 'border-red-200 dark:border-red-800',
            text: 'text-red-700 dark:text-red-300',
            icon: 'text-red-600 dark:text-red-400',
            dot: 'bg-red-500',
          };
      }
    }

    // Security level colors
    if (variant === 'status') {
      switch (status) {
        case 'high':
          return {
            bg: 'bg-green-50 dark:bg-green-900/20',
            border: 'border-green-200 dark:border-green-800',
            text: 'text-green-700 dark:text-green-300',
            icon: 'text-green-600 dark:text-green-400',
            dot: 'bg-green-500',
          };
        case 'medium':
          return {
            bg: 'bg-blue-50 dark:bg-blue-900/20',
            border: 'border-blue-200 dark:border-blue-800',
            text: 'text-blue-700 dark:text-blue-300',
            icon: 'text-blue-600 dark:text-blue-400',
            dot: 'bg-blue-500',
          };
        case 'low':
          return {
            bg: 'bg-amber-50 dark:bg-amber-900/20',
            border: 'border-amber-200 dark:border-amber-800',
            text: 'text-amber-700 dark:text-amber-300',
            icon: 'text-amber-600 dark:text-amber-400',
            dot: 'bg-amber-500',
          };
        case 'critical':
          return {
            bg: 'bg-red-50 dark:bg-red-900/20',
            border: 'border-red-200 dark:border-red-800',
            text: 'text-red-700 dark:text-red-300',
            icon: 'text-red-600 dark:text-red-400',
            dot: 'bg-red-500',
          };
      }
    }

    // Active/Inactive status colors
    if (status === 'active' || status === 'connected' || status === 'verified') {
      return {
        bg: 'bg-green-50 dark:bg-green-900/20',
        border: 'border-green-200 dark:border-green-800',
        text: 'text-green-700 dark:text-green-300',
        icon: 'text-green-600 dark:text-green-400',
        dot: 'bg-green-500',
      };
    }

    if (status === 'inactive' || status === 'disconnected' || status === 'unverified') {
      return {
        bg: 'bg-gray-50 dark:bg-gray-900/20',
        border: 'border-gray-200 dark:border-gray-800',
        text: 'text-gray-700 dark:text-gray-300',
        icon: 'text-gray-600 dark:text-gray-400',
        dot: 'bg-gray-500',
      };
    }

    // Default colors
    return {
      bg: 'bg-surface-secondary',
      border: 'border-border-secondary',
      text: 'text-text-secondary',
      icon: 'text-text-tertiary',
      dot: 'bg-text-tertiary',
    };
  };

  // Size variants
  const sizeClasses = {
    xs: {
      container: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      dot: 'w-1.5 h-1.5',
      gap: 'gap-1',
    },
    sm: {
      container: 'px-2.5 py-1.5 text-xs',
      icon: 'w-3.5 h-3.5',
      dot: 'w-2 h-2',
      gap: 'gap-1.5',
    },
    md: {
      container: 'px-3 py-2 text-sm',
      icon: 'w-4 h-4',
      dot: 'w-2.5 h-2.5',
      gap: 'gap-2',
    },
    lg: {
      container: 'px-4 py-2.5 text-base',
      icon: 'w-5 h-5',
      dot: 'w-3 h-3',
      gap: 'gap-2.5',
    },
  };

  const colors = getStatusColors();
  const currentSize = sizeClasses[size];

  // Format timestamp
  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <div
      className={designUtils.cn(
        'inline-flex items-center rounded-full border font-medium transition-all',
        colors.bg,
        colors.border,
        colors.text,
        currentSize.container,
        currentSize.gap,
        interactive && 'cursor-pointer hover:shadow-md hover:-translate-y-0.5',
        animated && isVisible && 'animate-fade-in',
        pulseActive && 'animate-pulse',
        className
      )}
      onClick={interactive ? onClick : undefined}
      role={interactive ? 'button' : undefined}
      aria-label={ariaLabel || `${variant} ${status}${label ? `: ${label}` : ''}`}
      tabIndex={interactive ? 0 : undefined}
    >
      {/* Status Dot */}
      <div className="relative flex-shrink-0">
        <div className={designUtils.cn('rounded-full', colors.dot, currentSize.dot)} />
        {pulseActive && (
          <div className={designUtils.cn(
            'absolute inset-0 rounded-full animate-ping',
            colors.dot,
            'opacity-75'
          )} />
        )}
      </div>

      {/* Icon */}
      {showIcon && (
        <div className={designUtils.cn('flex-shrink-0', colors.icon, currentSize.icon)}>
          {getIcon()}
        </div>
      )}

      {/* Label */}
      {label && (
        <span className="font-medium">
          {label}
        </span>
      )}

      {/* Status Text */}
      {!label && (
        <span className="font-medium capitalize">
          {status.toString().replace(/([A-Z])/g, ' $1').trim()}
        </span>
      )}

      {/* Timestamp */}
      {timestamp && (
        <span className="text-xs opacity-75 ml-1">
          {formatTimestamp(timestamp)}
        </span>
      )}
    </div>
  );
};

export default SecurityBadge;