import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const languages = [
    { code: 'en', name: 'EN' },
    { code: 'fr', name: 'FR' },
    { code: 'es', name: 'ES' },
  ];

  return (
    <div className="flex items-center">
      <Globe className="h-4 w-4 text-primary-400 mr-2" />
      <div className="flex space-x-1">
        {languages.map((lang) => (
          <button
            key={lang.code}
            onClick={() => i18n.changeLanguage(lang.code)}
            className={`px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${
              i18n.language === lang.code
                ? 'bg-primary-500 text-dark-900 shadow-neon'
                : 'text-primary-400 hover:text-primary-300 hover:bg-dark-800 border border-dark-700 hover:border-primary-500/50'
            }`}
          >
            {lang.name}
          </button>
        ))}
      </div>
    </div>
  );
};

export default LanguageSwitcher;