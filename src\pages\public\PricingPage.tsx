import React from 'react';
import { useTranslation } from 'react-i18next';
import { Check, AlertCircle, X, Shield, Lock, Eye, FileText, Star } from 'lucide-react';
// PublicLayout is already provided in App.tsx
import CTASection from '../../components/public/CTASection';
import FeatureStatusIndicator from '../../components/public/FeatureStatusIndicator';
import FeatureMatrix from '../../components/public/FeatureMatrix';

interface PricingFeature {
  name: string;
  available: boolean;
  status: 'available' | 'development' | 'planned';
  description?: string;
}

interface PricingPlan {
  name: string;
  price: string;
  description: string;
  features: PricingFeature[];
  cta: string;
  popular?: boolean;
  comingSoon?: boolean;
}

const PricingPage: React.FC = () => {
  const { t } = useTranslation(['pricing']);

  const plans: PricingPlan[] = [
    {
      name: 'Free',
      price: '$0',
      description: 'Essential OTP management for personal use. No credit card required.',
      features: [
        { name: 'Unlimited OTP Storage', available: true, status: 'available' },
        { name: 'Zero-Knowledge Encryption', available: true, status: 'available' },
        { name: 'Multi-Device Sync', available: true, status: 'available' },
        { name: 'Backup & Recovery Keys', available: true, status: 'available' },
        { name: 'Basic API Access (100 calls/month)', available: true, status: 'available' },
        { name: 'Community Support', available: true, status: 'available' },
        { name: 'Team Sharing', available: false, status: 'development' },
        { name: 'Priority Support', available: false, status: 'development' },
        { name: 'Advanced Analytics', available: false, status: 'development' },
        { name: 'SSO Integration', available: false, status: 'development' }
      ],
      cta: 'Start Free'
    },
    {
      name: 'Pro',
      price: '$1',
      description: 'Professional features for individuals and small teams.',
      features: [
        { name: 'Everything in Free', available: true, status: 'available' },
        { name: 'Team Sharing (up to 5 members)', available: true, status: 'development' },
        { name: 'Enhanced API Access (1,000 calls/month)', available: true, status: 'development' },
        { name: 'Priority Email Support', available: true, status: 'development' },
        { name: 'Advanced Backup Options', available: true, status: 'development' },
        { name: 'Usage Analytics Dashboard', available: true, status: 'development' },
        { name: 'Custom Folders & Organization', available: true, status: 'development' },
        { name: 'Export/Import Tools', available: true, status: 'development' },
        { name: 'SSO Integration', available: false, status: 'planned' },
        { name: 'Audit Logs', available: false, status: 'planned' }
      ],
      cta: 'Upgrade to Pro',
      popular: true,
      comingSoon: true
    },
    {
      name: 'Enterprise',
      price: '$5',
      description: 'For teams and organizations. Free up to 5 users, then $5 per user per month.',
      features: [
        { name: 'Everything in Pro', available: true, status: 'development' },
        { name: 'Unlimited Team Members', available: true, status: 'development' },
        { name: 'Advanced Admin Controls', available: true, status: 'development' },
        { name: 'Unlimited API Access', available: true, status: 'development' },
        { name: 'SSO & SAML Integration', available: true, status: 'planned' },
        { name: 'Compliance Reporting (SOC 2, GDPR)', available: true, status: 'planned' },
        { name: 'Dedicated Support Manager', available: true, status: 'planned' },
        { name: 'Custom Integrations', available: true, status: 'planned' },
        { name: 'SLA Guarantees (99.9% uptime)', available: true, status: 'planned' },
        { name: 'On-Premise Deployment Option', available: true, status: 'planned' }
      ],
      cta: 'Contact Sales',
      comingSoon: true
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="section bg-slate-50">
        <div className="container text-center">
          <h1 className="text-4xl font-bold text-primary mb-6">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-secondary max-w-3xl mx-auto mb-8">
            Choose the plan that fits your needs. No hidden fees, no surprises.
            Start free and upgrade when you're ready.
          </p>
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100 text-amber-800 rounded-lg text-sm font-medium">
            <span>💡</span>
            <span>New platform - some features are still in development</span>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="section">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">Choose Your Plan</h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              All plans include our core security features. Upgrade anytime as your needs grow.
            </p>
          </div>

          <div className="card-grid-3">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`card ${plan.popular ? 'card-elevated border-2 border-accent' : ''} relative`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="badge badge-primary px-4 py-1 text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="card-header">
                  <h3 className="card-title">{plan.name}</h3>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-4xl font-bold text-primary">{plan.price}</span>
                    {plan.price !== 'Custom' && plan.price !== '$0' && (
                      <span className="ml-1 text-lg text-tertiary">/month per user</span>
                    )}
                    {plan.price === '$0' && (
                      <span className="ml-1 text-lg text-tertiary">forever</span>
                    )}
                  </div>
                  <p className="card-subtitle mt-3">{plan.description}</p>

                  {plan.comingSoon && (
                    <div className="mt-4">
                      <span className="badge badge-warning">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        In Development
                      </span>
                    </div>
                  )}

                </div>

                <div className="card-body">
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIdx) => (
                      <li key={featureIdx} className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {feature.available ? (
                            <Check className="w-4 h-4 text-success" />
                          ) : (
                            <X className="w-4 h-4 text-muted" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className={`text-sm ${feature.available ? 'text-primary' : 'text-muted'}`}>
                              {feature.name}
                            </span>
                            {feature.status !== 'available' && (
                              <FeatureStatusIndicator
                                status={feature.status}
                                showText={false}
                                className="text-xs"
                              />
                            )}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="card-footer">
                  <a
                    href={plan.comingSoon ? '#waitlist' : '/auth'}
                    className={`btn w-full ${
                      plan.popular
                        ? 'btn-primary'
                        : 'btn-secondary'
                    }`}
                  >
                    {plan.comingSoon ? 'Join Waitlist' : plan.cta}
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section bg-slate-50">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary">Frequently Asked Questions</h2>
            <p className="mt-4 text-lg text-secondary max-w-3xl mx-auto">
              Honest answers to common questions about our platform and pricing.
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                Is the Free plan really free forever?
              </h3>
              <p className="text-secondary">
                Yes, absolutely. The Free plan includes all core OTP management features with no time limits.
                We believe everyone deserves secure authentication.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                Why are some features marked as "In Development"?
              </h3>
              <p className="text-secondary">
                We're a new platform focused on building the best OTP management solution. Rather than promise
                features we don't have yet, we're transparent about our development roadmap. All core security
                features are fully functional.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                How does the Enterprise pricing work?
              </h3>
              <p className="text-secondary">
                Enterprise plans are freemium up to 5 users. After that, it's $5 per user per month.
                If you need Pro features for your team, that's an additional $2 per month (not per user).
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                Can I trust you with my OTP secrets?
              </h3>
              <p className="text-secondary">
                We use zero-knowledge encryption, meaning we literally cannot access your OTP secrets even if we wanted to.
                Your secondary password never leaves your device, and all data is encrypted client-side.
              </p>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary mb-3">
                What happens if I lose my secondary password?
              </h3>
              <p className="text-secondary">
                Your data will be permanently inaccessible. This is by design for maximum security.
                We cannot recover your data - no one can. Please store your secondary password securely.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Waitlist Section */}
      <section id="waitlist" className="section">
        <div className="container">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-primary mb-6">Join the Waitlist</h2>
            <p className="text-lg text-secondary mb-8">
              Be the first to know when Pro and Enterprise features are ready.
              No spam, just important updates.
            </p>

            <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <div className="flex-1">
                <label htmlFor="email" className="sr-only">Email address</label>
                <input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  className="form-input"
                  required
                />
              </div>
              <button
                type="submit"
                className="btn btn-primary"
              >
                Join Waitlist
              </button>
            </form>

            <p className="mt-4 text-sm text-muted">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>

      {/* Feature Matrix */}
      <FeatureMatrix />

      {/* Enterprise Features - Fixed Colors */}
      <section className="section bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
        </div>

        <div className="container relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium mb-6 backdrop-blur-sm border border-blue-400/20">
              <Star className="w-4 h-4" />
              Enterprise Solutions
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Enterprise-Grade Security</h2>
            <p className="text-lg md:text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Built for organizations that need advanced security, compliance, and management features with enterprise-level support.
            </p>
          </div>

          <div className="grid gap-6 md:gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-blue-500/20 text-blue-300 mb-6 group-hover:scale-110 transition-transform">
                <Lock className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">
                Zero-Knowledge Architecture
              </h3>
              <p className="text-blue-100 leading-relaxed">
                Your data is encrypted client-side. We cannot access your OTP secrets even if compelled by law.
              </p>
            </div>

            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-emerald-500/20 text-emerald-300 mb-6 group-hover:scale-110 transition-transform">
                <Shield className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">
                Team Management
              </h3>
              <p className="text-blue-100 leading-relaxed">
                Role-based access controls, team sharing, and centralized administration for your organization.
              </p>
            </div>

            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-purple-500/20 text-purple-300 mb-6 group-hover:scale-110 transition-transform">
                <Eye className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">
                Compliance Ready
              </h3>
              <p className="text-blue-100 leading-relaxed">
                SOC 2, GDPR compliance features, audit logs, and reporting tools for enterprise requirements.
              </p>
            </div>

            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-amber-500/20 text-amber-300 mb-6 group-hover:scale-110 transition-transform">
                <FileText className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">
                API & Integrations
              </h3>
              <p className="text-blue-100 leading-relaxed">
                Comprehensive REST API, SSO integration, and custom development support for your workflow.
              </p>
            </div>
          </div>

          <div className="text-center mt-12">
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-xl hover:bg-blue-50 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Contact Sales Team
            </a>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <CTASection />
    </>
  );
};

export default PricingPage;