import React from 'react';
import { Shield, Lock, RefreshCw, Share2, Smartphone, Cloud, Code, Users, BarChart3, Settings, CheckCircle, AlertCircle, Eye, FileText } from 'lucide-react';

const FeaturesPage: React.FC = () => {
  const coreFeatures = [
    {
      icon: Lock,
      title: 'Zero-Knowledge Encryption',
      description: 'Your OTP secrets are encrypted client-side with your secondary password. We never see your plaintext data, ensuring complete privacy.',
      status: 'available'
    },
    {
      icon: RefreshCw,
      title: 'Multi-Device Sync',
      description: 'Access your OTP codes from any device. Encrypted data syncs securely across all your devices in real-time.',
      status: 'available'
    },
    {
      icon: Shield,
      title: 'Backup & Recovery',
      description: 'Secure recovery key system protects against data loss. Your recovery key is generated once and never stored on our servers.',
      status: 'available'
    },
    {
      icon: Code,
      title: 'Developer API',
      description: 'REST API for automation and integration. Perfect for DevOps teams and custom workflows.',
      status: 'available'
    },
    {
      icon: Share2,
      title: 'Secure Sharing',
      description: 'Share OTP secrets with team members or via secure URLs. Temporary and permanent sharing options available.',
      status: 'development'
    },
    {
      icon: Users,
      title: 'Social Login Support',
      description: 'Login with Facebook, GitHub, Google, Microsoft, and LinkedIn for convenient access.',
      status: 'development'
    }
  ];

  const enterpriseFeatures = [
    {
      icon: BarChart3,
      title: 'Team Management',
      description: 'Invite team members, assign roles, and manage access to shared OTP secrets.',
      status: 'development'
    },
    {
      icon: Settings,
      title: 'Folder Organization',
      description: 'Organize OTP secrets into folders with team-based access controls and sharing.',
      status: 'development'
    },
    {
      icon: Cloud,
      title: 'API Integration',
      description: 'REST API for all account types to integrate OTP management into existing workflows.',
      status: 'available'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="w-4 h-4 text-success" />;
      case 'development':
        return <AlertCircle className="w-4 h-4 text-warning" />;
      case 'planned':
        return <div className="w-4 h-4 rounded-full border-2 border-muted"></div>;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'development':
        return 'In Development';
      case 'planned':
        return 'Planned';
      default:
        return '';
    }
  };

  return (
    <>
      {/* Hero Section */}
      <section className="section bg-slate-50">
        <div className="container text-center">
          <h1 className="text-4xl font-bold text-primary mb-6">
            Cloud OTP Platform Features
          </h1>
          <p className="text-xl text-secondary max-w-3xl mx-auto mb-8">
            Secure, cloud-based OTP management with zero-knowledge encryption.
            Built for individuals, teams, and organizations seeking device-independent authentication.
          </p>
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100 text-amber-800 rounded-lg text-sm font-medium">
            <span>⚠️</span>
            <span>New platform - some features are still in development</span>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="section">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">Core Features</h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              Essential OTP management capabilities available to all users.
            </p>
          </div>

          <div className="card-grid">
            {coreFeatures.map((feature, index) => (
              <div key={index} className="card hover-lift">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100 text-blue-600">
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(feature.status)}
                    <span className="text-xs font-medium text-muted">
                      {getStatusText(feature.status)}
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-primary mb-3">
                  {feature.title}
                </h3>
                <p className="text-secondary">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enterprise Features */}
      <section className="section bg-slate-50">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">Enterprise Features</h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              Advanced capabilities for organizations and teams.
            </p>
          </div>

          <div className="card-grid">
            {enterpriseFeatures.map((feature, index) => (
              <div key={index} className="card hover-lift">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-purple-100 text-purple-600">
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(feature.status)}
                    <span className="text-xs font-medium text-muted">
                      {getStatusText(feature.status)}
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-primary mb-3">
                  {feature.title}
                </h3>
                <p className="text-secondary">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Security Section - Fixed Colors */}
      <section className="section bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
        </div>

        <div className="container relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium mb-6 backdrop-blur-sm border border-blue-400/20">
              <Shield className="w-4 h-4" />
              Enterprise-Grade Protection
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Security First</h2>
            <p className="text-lg md:text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              Built with enterprise-grade security from the ground up. Your data protection is our highest priority.
            </p>
          </div>

          <div className="grid gap-6 md:gap-8 grid-cols-1 md:grid-cols-2 max-w-5xl mx-auto">
            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-blue-500/20 text-blue-300 mb-6 group-hover:scale-110 transition-transform">
                <Lock className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">Client-Side Encryption</h3>
              <p className="text-blue-100 leading-relaxed">
                All encryption happens on your device. We never see your OTP secrets in plain text, ensuring complete privacy.
              </p>
            </div>

            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-emerald-500/20 text-emerald-300 mb-6 group-hover:scale-110 transition-transform">
                <Shield className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">Zero-Knowledge Architecture</h3>
              <p className="text-blue-100 leading-relaxed">
                Even if our servers are compromised, your data remains secure and inaccessible to anyone, including us.
              </p>
            </div>

            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-purple-500/20 text-purple-300 mb-6 group-hover:scale-110 transition-transform">
                <Eye className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">Open Source Security</h3>
              <p className="text-blue-100 leading-relaxed">
                Our encryption implementation is open source and auditable by security experts worldwide.
              </p>
            </div>

            <div className="p-6 md:p-8 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 group">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-amber-500/20 text-amber-300 mb-6 group-hover:scale-110 transition-transform">
                <FileText className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">No Recovery Backdoors</h3>
              <p className="text-blue-100 leading-relaxed">
                If you lose your secondary password, your data is gone forever. This is by design for maximum security.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section">
        <div className="container text-center">
          <h2 className="text-3xl font-bold text-primary mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-lg text-secondary mb-8 max-w-2xl mx-auto">
            Experience professional OTP management with enterprise-grade security.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/auth" className="btn btn-primary btn-lg">
              Start Free
            </a>
            <a href="/pricing" className="btn btn-secondary btn-lg">
              View Pricing
            </a>
          </div>
        </div>
      </section>
    </>
  );
};

export default FeaturesPage;
