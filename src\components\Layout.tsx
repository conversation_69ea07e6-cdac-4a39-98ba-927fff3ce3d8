import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import SEO from './SEO';
import AppNavbar from './AppNavbar';
import FooterLanguageSelector from './FooterLanguageSelector';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  useSEO?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ children, title, description, useSEO = true }) => {
  const { t } = useTranslation(['common', 'otp', 'profile', 'settings']);
  const location = useLocation();

  // Determine page title based on current path if not provided
  const pageTitle = title || (() => {
    const path = location.pathname;
    if (path === '/app') return t('otp:list');
    if (path.startsWith('/app/otp/')) return t('otp:detailTitle');
    if (path === '/app/add') return t('otp:add');
    if (path === '/app/profile') return t('profile:title');
    if (path === '/app/settings') return t('settings:title');
    return t('common:appName');
  })();

  return (
    <div className="min-h-screen bg-dark-900 text-white">
      {useSEO && <SEO title={pageTitle} description={description} />}
      <AppNavbar />

      {/* Éléments décoratifs pixelisés */}
      <div className="fixed top-20 right-10 w-32 h-32 opacity-20 z-0">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px] animate-pulse-slow"></div>
      </div>
      <div className="fixed bottom-20 left-10 w-40 h-40 opacity-20 z-0">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:15px_15px] animate-pulse-slow"></div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-1">
        <div className="bg-dark-800/90 backdrop-blur-sm rounded-2xl border border-dark-700 shadow-xl p-6 sm:p-8">
          {children}
        </div>
      </main>

      <footer className="mt-12 py-6 bg-dark-800 border-t border-dark-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col items-center justify-center space-y-4">
          <div className="language-selector-footer">
            <FooterLanguageSelector />
          </div>
          <p className="text-center text-sm text-dark-400">© {new Date().getFullYear()} OTP Manager. {t('common:footerText')}</p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;