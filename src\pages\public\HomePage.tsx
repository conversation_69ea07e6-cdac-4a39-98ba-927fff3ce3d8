import React from 'react';
import {
  Shield,
  Lock,
  Users,
  Zap,
  ArrowRight,
  CheckCircle,
  Globe,
  Smartphone,
  Server,
  Eye,
  Clock,
  Building2,
  Star,
  TrendingUp,
  Database,
  Key,
  FileText,
  Layers
} from 'lucide-react';

const HomePage: React.FC = () => {
  return (
    <>
      {/* Hero Section - Enhanced with Professional Design */}
      <section className="section-lg bg-gradient-to-br from-slate-50 via-blue-50 to-slate-100">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-6">
                <Star className="w-4 h-4" />
                New Platform - Core Features Available
              </div>

              <h1 className="text-6xl font-bold text-primary mb-6 leading-tight">
                Cloud-Based OTP Management
                <span className="block text-accent">Without Device Dependencies</span>
                <span className="block text-3xl font-medium text-secondary mt-2">
                  Zero-Knowledge Security
                </span>
              </h1>

              <p className="text-xl text-secondary mb-8 max-w-3xl mx-auto leading-relaxed">
                Secure cloud-based platform for managing TOTP/HOTP secrets with client-side encryption.
                Access your OTP codes from any device while maintaining complete privacy.
                Built for individuals, teams, and organizations seeking device-independent authentication.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <a href="/auth" className="btn btn-primary btn-xl">
                  Start Free Trial
                  <ArrowRight className="w-5 h-5 ml-2" />
                </a>
                
                <a href="/features" className="btn btn-outline btn-xl">
                  Explore Features
                </a>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div className="flex items-center justify-center gap-2 text-muted">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Free tier available</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-muted">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Zero-knowledge encryption</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-muted">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Enterprise compliance ready</span>
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="card bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-primary mb-4">
                  Built for Professional Security Standards
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent">256-bit</div>
                    <div className="text-sm text-secondary">AES Encryption</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent">Zero</div>
                    <div className="text-sm text-secondary">Knowledge Access</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent">99.9%</div>
                    <div className="text-sm text-secondary">Uptime SLA</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent">24/7</div>
                    <div className="text-sm text-secondary">Support Available</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features Section - Enhanced & Responsive */}
      <section className="section-lg bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="container">
          <div className="text-center mb-16 lg:mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-6">
              <Layers className="w-4 h-4" />
              Enterprise-Grade Features
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-6 leading-tight">
              Professional OTP Management
              <span className="block text-2xl md:text-3xl lg:text-4xl text-accent mt-2">
                Built for Modern Teams
              </span>
            </h2>
            <p className="text-lg md:text-xl text-secondary max-w-4xl mx-auto leading-relaxed">
              Engineered for security professionals, development teams, and enterprises requiring
              reliable, auditable, and scalable two-factor authentication management with zero compromises.
            </p>
          </div>

          {/* Feature Grid - Responsive Design */}
          <div className="grid gap-6 md:gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mb-16">
            {/* Zero-Knowledge Architecture */}
            <div className="card hover-lift group">
              <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                <Lock className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-4 group-hover:text-accent transition-colors">
                Zero-Knowledge Architecture
              </h3>
              <p className="text-secondary mb-6 leading-relaxed">
                Client-side encryption ensures your OTP secrets remain completely private.
                Your secondary password never leaves your device, providing true zero-knowledge security.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>AES-256 encryption standard</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Client-side key derivation</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>No plaintext data storage</span>
                </div>
              </div>
            </div>

            {/* Universal Device Access */}
            <div className="card hover-lift group">
              <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-100 to-emerald-200 text-emerald-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                <Globe className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-4 group-hover:text-accent transition-colors">
                Universal Device Access
              </h3>
              <p className="text-secondary mb-6 leading-relaxed">
                Access your authentication codes from any device, anywhere. Real-time
                synchronization keeps your data current across all platforms.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Cross-platform compatibility</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Real-time synchronization</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Offline backup capabilities</span>
                </div>
              </div>
            </div>

            {/* Enterprise Team Management */}
            <div className="card hover-lift group">
              <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                <Users className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-4 group-hover:text-accent transition-colors">
                Team Collaboration
              </h3>
              <p className="text-secondary mb-6 leading-relaxed">
                Share OTP secrets securely with team members using encrypted sharing and
                role-based access controls. Perfect for team environments.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-amber-500" />
                  <span>Secure OTP sharing</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-amber-500" />
                  <span>Team member management</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-amber-500" />
                  <span>Access control features</span>
                </div>
              </div>
            </div>

            {/* Developer-First API */}
            <div className="card hover-lift group">
              <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-amber-100 to-amber-200 text-amber-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                <Zap className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-4 group-hover:text-accent transition-colors">
                Developer-First API
              </h3>
              <p className="text-secondary mb-6 leading-relaxed">
                Comprehensive REST API for seamless integration into your existing
                workflows and automation systems.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>RESTful API design</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Rate limiting & quotas</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Webhook integrations</span>
                </div>
              </div>
            </div>

            {/* Scalable Infrastructure */}
            <div className="card hover-lift group">
              <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-100 to-indigo-200 text-indigo-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                <Database className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-4 group-hover:text-accent transition-colors">
                Scalable Infrastructure
              </h3>
              <p className="text-secondary mb-6 leading-relaxed">
                Built on enterprise-grade infrastructure with 99.9% uptime SLA
                and automatic scaling capabilities.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>99.9% uptime guarantee</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Auto-scaling architecture</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Global CDN distribution</span>
                </div>
              </div>
            </div>

            {/* Compliance Ready */}
            <div className="card hover-lift group">
              <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-rose-100 to-rose-200 text-rose-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                <FileText className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-semibold text-primary mb-4 group-hover:text-accent transition-colors">
                Compliance Ready
              </h3>
              <p className="text-secondary mb-6 leading-relaxed">
                Meet enterprise compliance requirements with comprehensive audit
                trails, data governance, and security certifications.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>GDPR & CCPA compliant</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>SOC 2 Type II ready</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-tertiary">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span>Enterprise audit trails</span>
                </div>
              </div>
            </div>
          </div>

          {/* Call-to-Action for Features */}
          <div className="text-center">
            <div className="card bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                <div className="text-center md:text-left">
                  <h3 className="text-2xl font-semibold text-primary mb-2">
                    Ready to Experience Professional OTP Management?
                  </h3>
                  <p className="text-secondary">
                    Start with our free tier and discover why security professionals choose Cloud OTP.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-3">
                  <a href="/auth" className="btn btn-primary">
                    Start Free Trial
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </a>
                  <a href="/features" className="btn btn-outline">
                    Explore All Features
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Transparent Development Status */}
      <section className="section bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-primary mb-6">
                Transparent Development Roadmap
              </h2>
              <p className="text-xl text-secondary mb-8 max-w-3xl mx-auto">
                We believe in honest communication about our platform's capabilities.
                Here's exactly what's available today and what's actively in development.
              </p>
            </div>

            <div className="grid gap-8 lg:grid-cols-2">
              <div className="card card-elevated">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-4 h-4 rounded-full bg-success"></div>
                  <h3 className="text-2xl font-semibold text-primary">Production Ready</h3>
                  <span className="badge badge-success">Available Now</span>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Unlimited OTP Storage</div>
                      <div className="text-sm text-secondary">Store unlimited TOTP/HOTP secrets with no restrictions</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Zero-Knowledge Encryption</div>
                      <div className="text-sm text-secondary">Client-side AES-256 encryption with secondary password</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Multi-Device Synchronization</div>
                      <div className="text-sm text-secondary">Real-time sync across all your devices</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Secure Backup & Recovery</div>
                      <div className="text-sm text-secondary">Encrypted backup system with recovery keys</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">REST API Access</div>
                      <div className="text-sm text-secondary">Basic API endpoints for automation</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-success mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Professional Support</div>
                      <div className="text-sm text-secondary">Email support with enterprise SLA options</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card card-elevated">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-4 h-4 rounded-full bg-warning"></div>
                  <h3 className="text-2xl font-semibold text-primary">In Active Development</h3>
                  <span className="badge badge-warning">Q1-Q2 2025</span>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-warning mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Team Collaboration</div>
                      <div className="text-sm text-secondary">Secure team sharing with role-based permissions</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-warning mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Enterprise Analytics</div>
                      <div className="text-sm text-secondary">Usage analytics and security monitoring</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-warning mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">SSO Integration</div>
                      <div className="text-sm text-secondary">SAML, OIDC, and social login support</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-warning mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Mobile & Desktop Apps</div>
                      <div className="text-sm text-secondary">Native applications for all platforms</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-warning mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Advanced Enterprise Features</div>
                      <div className="text-sm text-secondary">Audit logs, compliance tools, and admin dashboards</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-warning mt-0.5" />
                    <div>
                      <div className="font-medium text-primary">Webhook Integrations</div>
                      <div className="text-sm text-secondary">Real-time event notifications for automation</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center mt-12">
              <p className="text-secondary mb-6">
                Want to influence our roadmap? We actively listen to our users and prioritize
                features based on real business needs.
              </p>
              <a href="/contact" className="btn btn-outline">
                Share Your Requirements
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Preview Section */}
      <section className="section">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-primary mb-6">
              Transparent, Honest Pricing
            </h2>
            <p className="text-xl text-secondary max-w-3xl mx-auto">
              No hidden fees, no surprise charges. Choose the plan that fits your needs,
              from individual use to enterprise deployment.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3 max-w-5xl mx-auto">
            {/* Free Tier */}
            <div className="card hover-lift">
              <div className="text-center">
                <h3 className="text-2xl font-semibold text-primary mb-2">Free</h3>
                <div className="text-4xl font-bold text-accent mb-4">$0</div>
                <p className="text-secondary mb-6">Perfect for personal use</p>

                <ul className="space-y-3 text-left mb-8">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Unlimited OTP storage</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Multi-device sync</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Basic API access</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Email support</span>
                  </li>
                </ul>

                <a href="/auth" className="btn btn-outline w-full">
                  Start Free
                </a>
              </div>
            </div>

            {/* Pro Tier */}
            <div className="card hover-lift border-accent">
              <div className="text-center">
                <div className="inline-flex items-center gap-2 px-3 py-1 bg-accent text-white rounded-full text-sm font-medium mb-4">
                  <Star className="w-4 h-4" />
                  Most Popular
                </div>
                <h3 className="text-2xl font-semibold text-primary mb-2">Pro</h3>
                <div className="text-4xl font-bold text-accent mb-1">$1</div>
                <div className="text-secondary mb-4">per month</div>
                <p className="text-secondary mb-6">For power users and small teams</p>

                <ul className="space-y-3 text-left mb-8">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Everything in Free</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Priority support</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Advanced API features</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Export capabilities</span>
                  </li>
                </ul>

                <a href="/auth" className="btn btn-primary w-full">
                  Start Pro Trial
                </a>
              </div>
            </div>

            {/* Enterprise Tier */}
            <div className="card hover-lift">
              <div className="text-center">
                <h3 className="text-2xl font-semibold text-primary mb-2">Enterprise</h3>
                <div className="text-4xl font-bold text-accent mb-1">$5</div>
                <div className="text-secondary mb-4">per user/month</div>
                <p className="text-secondary mb-6">For teams and organizations</p>

                <ul className="space-y-3 text-left mb-8">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Free up to 5 users</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Team collaboration</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">Admin dashboard</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-success" />
                    <span className="text-secondary">24/7 support</span>
                  </li>
                </ul>

                <a href="/contact" className="btn btn-secondary w-full">
                  Contact Sales
                </a>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-secondary mb-4">
              All plans include our core security features and zero-knowledge encryption.
            </p>
            <a href="/pricing" className="text-accent hover:text-accent-hover font-medium">
              View detailed pricing comparison →
            </a>
          </div>
        </div>
      </section>

      {/* Security & Trust Section */}
      <section className="section bg-slate-50">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-primary mb-6">
              Built on Trust & Transparency
            </h2>
            <p className="text-xl text-secondary max-w-3xl mx-auto">
              Security isn't just a feature—it's our foundation. Every decision we make
              prioritizes your data privacy and platform reliability.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-blue-100 text-blue-600 mx-auto mb-4">
                <Shield className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-semibold text-primary mb-2">
                Zero-Knowledge
              </h3>
              <p className="text-secondary text-sm">
                We cannot access your data even if legally compelled. Your encryption keys never leave your device.
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-emerald-100 text-emerald-600 mx-auto mb-4">
                <Server className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-semibold text-primary mb-2">
                Enterprise Infrastructure
              </h3>
              <p className="text-secondary text-sm">
                Built on enterprise-grade cloud infrastructure with 99.9% uptime SLA and automatic scaling.
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-purple-100 text-purple-600 mx-auto mb-4">
                <Eye className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-semibold text-primary mb-2">
                Open Development
              </h3>
              <p className="text-secondary text-sm">
                Transparent development process with public roadmap and regular security audits.
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-amber-100 text-amber-600 mx-auto mb-4">
                <Building2 className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-semibold text-primary mb-2">
                Compliance Ready
              </h3>
              <p className="text-secondary text-sm">
                GDPR, CCPA compliant with SOC 2 Type II certification in progress for enterprise customers.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section - Fixed Colors & Enhanced Design */}
      <section className="section-lg bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
        </div>

        <div className="container text-center relative z-10">
          <div className="max-w-5xl mx-auto">
            {/* Header */}
            <div className="mb-12">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium mb-6 backdrop-blur-sm border border-blue-400/20">
                <Star className="w-4 h-4" />
                Join Thousands of Security Professionals
              </div>

              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white leading-tight">
                Ready to Modernize Your
                <span className="block bg-gradient-to-r from-blue-300 to-indigo-300 bg-clip-text text-transparent mt-2">
                  Authentication Workflow?
                </span>
              </h2>

              <p className="text-lg md:text-xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed">
                Join security professionals who have moved beyond device-dependent authentication.
                Start with our free tier and scale as your needs grow—no credit card required.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center mb-16">
              <a
                href="/auth"
                className="inline-flex items-center justify-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-xl hover:bg-blue-50 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group"
              >
                <Key className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
                Start Free Account
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </a>

              <a
                href="/contact"
                className="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-blue-300 text-blue-100 font-semibold rounded-xl hover:bg-blue-300 hover:text-slate-900 hover:scale-105 transition-all duration-300 backdrop-blur-sm group"
              >
                <Building2 className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Enterprise Demo
              </a>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 text-sm text-blue-200 mb-12">
              <div className="flex items-center justify-center gap-3 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                <span>No credit card required</span>
              </div>
              <div className="flex items-center justify-center gap-3 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                <span>30-day enterprise trial</span>
              </div>
              <div className="flex items-center justify-center gap-3 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                <span>Migration assistance included</span>
              </div>
            </div>

            {/* Company Attribution */}
            <div className="text-center">
              <p className="text-blue-300/80 text-sm">
                Proudly developed by <span className="font-semibold text-blue-200">Leaf SARL-U</span> • Togo
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default HomePage;