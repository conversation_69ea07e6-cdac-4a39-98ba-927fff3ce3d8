import React, { useState, useEffect } from 'react';
import { Save, Edit, Eye, AlertCircle, CheckCircle } from 'lucide-react';

interface ContentSection {
  id: string;
  name: string;
  description: string;
  content: string;
  status: 'available' | 'development' | 'planned';
  lastUpdated: string;
}

const ContentAdminPage: React.FC = () => {
  const [sections, setSections] = useState<ContentSection[]>([]);
  const [selectedSection, setSelectedSection] = useState<ContentSection | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContentSections();
  }, []);

  const loadContentSections = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would fetch from your CMS or database
      // For now, we'll use static data representing the current content structure
      const mockSections: ContentSection[] = [
        {
          id: 'hero-title',
          name: 'Homepage Hero Title',
          description: 'Main headline on the homepage',
          content: 'Zero-Knowledge OTP Security for the Modern Enterprise',
          status: 'available',
          lastUpdated: '2024-12-01'
        },
        {
          id: 'hero-subtitle',
          name: 'Homepage Hero Subtitle',
          description: 'Subtitle text under the main headline',
          content: 'Secure your authentication with military-grade encryption that only you can decrypt. Access your OTP codes from anywhere while maintaining complete privacy.',
          status: 'available',
          lastUpdated: '2024-12-01'
        },
        {
          id: 'features-zero-knowledge',
          name: 'Zero-Knowledge Feature Description',
          description: 'Description of the zero-knowledge security feature',
          content: 'Your OTP secrets are encrypted client-side with your own password. We never see your plaintext data.',
          status: 'available',
          lastUpdated: '2024-12-01'
        },
        {
          id: 'features-team-sharing',
          name: 'Team Sharing Feature Description',
          description: 'Description of team sharing capabilities',
          content: 'Share OTP codes securely with team members while maintaining zero-knowledge encryption.',
          status: 'development',
          lastUpdated: '2024-12-01'
        },
        {
          id: 'pricing-free-description',
          name: 'Free Plan Description',
          description: 'Description of the free pricing tier',
          content: 'Perfect for personal use and getting started with secure OTP management.',
          status: 'available',
          lastUpdated: '2024-12-01'
        },
        {
          id: 'pricing-pro-description',
          name: 'Pro Plan Description',
          description: 'Description of the pro pricing tier',
          content: 'Enhanced features for power users and small teams.',
          status: 'development',
          lastUpdated: '2024-12-01'
        }
      ];

      setSections(mockSections);
    } catch (error) {
      console.error('Error loading content sections:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditSection = (section: ContentSection) => {
    setSelectedSection({ ...section });
    setIsEditing(true);
  };

  const handleSaveSection = async (section: ContentSection) => {
    try {
      // In a real implementation, this would save to your CMS or database
      const updatedSections = sections.map(s => 
        s.id === section.id 
          ? { ...section, lastUpdated: new Date().toISOString().split('T')[0] }
          : s
      );
      setSections(updatedSections);
      setIsEditing(false);
      setSelectedSection(null);
      
      // Show success message
      alert('Content updated successfully!');
    } catch (error) {
      console.error('Error saving section:', error);
      alert('Error saving content. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'development':
        return 'bg-amber-100 text-amber-800';
      case 'planned':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'development':
        return <AlertCircle className="h-4 w-4 text-amber-600" />;
      case 'planned':
        return <Eye className="h-4 w-4 text-gray-600" />;
      default:
        return <Eye className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
        <p className="mt-2 text-gray-600">Manage marketing copy and feature descriptions</p>
      </div>

      {/* Content Sections List */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Content Sections</h2>
        </div>

        <div className="divide-y divide-gray-200">
          {sections.map((section) => (
            <div key={section.id} className="px-6 py-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {section.name}
                    </h3>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(section.status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(section.status)}`}>
                        {section.status}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-2">{section.description}</p>
                  <div className="bg-gray-50 rounded-lg p-3 mb-2">
                    <p className="text-sm text-gray-700 line-clamp-2">{section.content}</p>
                  </div>
                  <div className="text-sm text-gray-500">
                    Last updated: {section.lastUpdated}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEditSection(section)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Edit Modal */}
      {isEditing && selectedSection && (
        <ContentEditModal
          section={selectedSection}
          onSave={handleSaveSection}
          onCancel={() => {
            setIsEditing(false);
            setSelectedSection(null);
          }}
          onChange={setSelectedSection}
        />
      )}
    </div>
  );
};

interface ContentEditModalProps {
  section: ContentSection;
  onSave: (section: ContentSection) => void;
  onCancel: () => void;
  onChange: (section: ContentSection) => void;
}

const ContentEditModal: React.FC<ContentEditModalProps> = ({
  section,
  onSave,
  onCancel,
  onChange
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(section);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Edit Content: {section.name}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Name
            </label>
            <input
              type="text"
              value={section.name}
              onChange={(e) => onChange({ ...section, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <input
              type="text"
              value={section.description}
              onChange={(e) => onChange({ ...section, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content
            </label>
            <textarea
              value={section.content}
              onChange={(e) => onChange({ ...section, content: e.target.value })}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter the content text"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={section.status}
              onChange={(e) => onChange({ ...section, status: e.target.value as 'available' | 'development' | 'planned' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="available">Available</option>
              <option value="development">In Development</option>
              <option value="planned">Planned</option>
            </select>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContentAdminPage;