import { supabase } from '../lib/supabase';

export interface ChangelogEntry {
  id?: string;
  version: string;
  date: string;
  title: string;
  description: string;
  changes: string[];
  status: 'draft' | 'published' | 'archived';
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface ChangelogCategory {
  id: string;
  name: string;
  description: string;
  order: number;
}

class ChangelogService {
  async getPublishedEntries(): Promise<ChangelogEntry[]> {
    try {
      const { data, error } = await supabase
        .from('changelog_entries')
        .select('*')
        .eq('status', 'published')
        .order('date', { ascending: false });

      if (error) {
        console.error('Error fetching changelog entries:', error);
        return this.getFallbackEntries();
      }

      return data || this.getFallbackEntries();
    } catch (error) {
      console.error('Error in getPublishedEntries:', error);
      return this.getFallbackEntries();
    }
  }

  async getAllEntries(): Promise<ChangelogEntry[]> {
    try {
      const { data, error } = await supabase
        .from('changelog_entries')
        .select('*')
        .order('date', { ascending: false });

      if (error) {
        console.error('Error fetching all changelog entries:', error);
        return this.getFallbackEntries();
      }

      return data || this.getFallbackEntries();
    } catch (error) {
      console.error('Error in getAllEntries:', error);
      return this.getFallbackEntries();
    }
  }

  async createEntry(entry: Omit<ChangelogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<ChangelogEntry | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('changelog_entries')
        .insert([{
          ...entry,
          created_by: user?.id
        }])
        .select()
        .single();

      if (error) {
        console.error('Error creating changelog entry:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in createEntry:', error);
      return null;
    }
  }

  async updateEntry(id: string, updates: Partial<ChangelogEntry>): Promise<ChangelogEntry | null> {
    try {
      const { data, error } = await supabase
        .from('changelog_entries')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating changelog entry:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateEntry:', error);
      return null;
    }
  }

  async deleteEntry(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('changelog_entries')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting changelog entry:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteEntry:', error);
      return false;
    }
  }

  async publishEntry(id: string): Promise<boolean> {
    return this.updateEntry(id, { status: 'published' }) !== null;
  }

  async archiveEntry(id: string): Promise<boolean> {
    return this.updateEntry(id, { status: 'archived' }) !== null;
  }

  // Fallback entries for when database is not available
  private getFallbackEntries(): ChangelogEntry[] {
    return [
      {
        id: 'fallback-1',
        version: '0.1.0-alpha.1',
        date: '2024-12-01',
        title: 'First Alpha Version',
        description: 'Initial alpha release of Cloud OTP with core functionality for secure OTP management.',
        status: 'published',
        changes: [
          'Complete authentication system with Supabase',
          'User account creation with email/password',
          'Zero-knowledge client-side encryption',
          'Add, view, and manage TOTP/HOTP codes',
          'Secure backup and recovery system',
          'Multi-language support (English, French, Spanish)',
          'Responsive design with modern UI',
          'Basic API endpoints for OTP management'
        ]
      }
    ];
  }

  // Get categories for organizing changelog entries
  async getCategories(): Promise<ChangelogCategory[]> {
    try {
      const { data, error } = await supabase
        .from('changelog_categories')
        .select('*')
        .order('order');

      if (error) {
        console.error('Error fetching changelog categories:', error);
        return this.getDefaultCategories();
      }

      return data || this.getDefaultCategories();
    } catch (error) {
      console.error('Error in getCategories:', error);
      return this.getDefaultCategories();
    }
  }

  private getDefaultCategories(): ChangelogCategory[] {
    return [
      { id: 'features', name: 'New Features', description: 'New functionality and capabilities', order: 1 },
      { id: 'improvements', name: 'Improvements', description: 'Enhancements to existing features', order: 2 },
      { id: 'security', name: 'Security', description: 'Security updates and fixes', order: 3 },
      { id: 'bugfixes', name: 'Bug Fixes', description: 'Resolved issues and bugs', order: 4 },
      { id: 'api', name: 'API Changes', description: 'API updates and modifications', order: 5 }
    ];
  }
}

export const changelogService = new ChangelogService();