/* Professional Enterprise Design System - Cloud OTP Platform 2025 */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== ENTERPRISE DESIGN SYSTEM FOUNDATION 2025 ===== */

/* Design Tokens - Atomic Values for Consistent UI */
:root {
  /* ===== PRIMITIVE COLOR TOKENS ===== */

  /* Neutral Palette - Professional Gray Scale */
  --primitive-neutral-0: #ffffff;
  --primitive-neutral-25: #fcfcfd;
  --primitive-neutral-50: #f8fafc;
  --primitive-neutral-100: #f1f5f9;
  --primitive-neutral-200: #e2e8f0;
  --primitive-neutral-300: #cbd5e1;
  --primitive-neutral-400: #94a3b8;
  --primitive-neutral-500: #64748b;
  --primitive-neutral-600: #475569;
  --primitive-neutral-700: #334155;
  --primitive-neutral-800: #1e293b;
  --primitive-neutral-900: #0f172a;
  --primitive-neutral-950: #020617;

  /* Primary Brand Palette - Professional Blue */
  --primitive-primary-50: #eff6ff;
  --primitive-primary-100: #dbeafe;
  --primitive-primary-200: #bfdbfe;
  --primitive-primary-300: #93c5fd;
  --primitive-primary-400: #60a5fa;
  --primitive-primary-500: #3b82f6;
  --primitive-primary-600: #2563eb;
  --primitive-primary-700: #1d4ed8;
  --primitive-primary-800: #1e40af;
  --primitive-primary-900: #1e3a8a;
  --primitive-primary-950: #172554;

  /* Success Palette - Professional Green */
  --primitive-success-50: #ecfdf5;
  --primitive-success-100: #d1fae5;
  --primitive-success-200: #a7f3d0;
  --primitive-success-300: #6ee7b7;
  --primitive-success-400: #34d399;
  --primitive-success-500: #10b981;
  --primitive-success-600: #059669;
  --primitive-success-700: #047857;
  --primitive-success-800: #065f46;
  --primitive-success-900: #064e3b;

  /* Warning Palette - Professional Amber */
  --primitive-warning-50: #fffbeb;
  --primitive-warning-100: #fef3c7;
  --primitive-warning-200: #fde68a;
  --primitive-warning-300: #fcd34d;
  --primitive-warning-400: #fbbf24;
  --primitive-warning-500: #f59e0b;
  --primitive-warning-600: #d97706;
  --primitive-warning-700: #b45309;
  --primitive-warning-800: #92400e;
  --primitive-warning-900: #78350f;

  /* Error Palette - Professional Red */
  --primitive-error-50: #fef2f2;
  --primitive-error-100: #fee2e2;
  --primitive-error-200: #fecaca;
  --primitive-error-300: #fca5a5;
  --primitive-error-400: #f87171;
  --primitive-error-500: #ef4444;
  --primitive-error-600: #dc2626;
  --primitive-error-700: #b91c1c;
  --primitive-error-800: #991b1b;
  --primitive-error-900: #7f1d1d;

  /* Info Palette - Professional Cyan */
  --primitive-info-50: #ecfeff;
  --primitive-info-100: #cffafe;
  --primitive-info-200: #a5f3fc;
  --primitive-info-300: #67e8f9;
  --primitive-info-400: #22d3ee;
  --primitive-info-500: #06b6d4;
  --primitive-info-600: #0891b2;
  --primitive-info-700: #0e7490;
  --primitive-info-800: #155e75;
  --primitive-info-900: #164e63;

  /* ===== SEMANTIC COLOR TOKENS ===== */

  /* Brand Colors - Primary Identity */
  --color-brand-primary: var(--primitive-primary-600);
  --color-brand-primary-hover: var(--primitive-primary-700);
  --color-brand-primary-active: var(--primitive-primary-800);
  --color-brand-primary-subtle: var(--primitive-primary-50);
  --color-brand-primary-muted: var(--primitive-primary-100);

  /* Text Colors - Hierarchical Content */
  --color-text-primary: var(--primitive-neutral-900);
  --color-text-secondary: var(--primitive-neutral-700);
  --color-text-tertiary: var(--primitive-neutral-500);
  --color-text-quaternary: var(--primitive-neutral-400);
  --color-text-disabled: var(--primitive-neutral-300);
  --color-text-inverse: var(--primitive-neutral-0);
  --color-text-brand: var(--primitive-primary-600);
  --color-text-success: var(--primitive-success-700);
  --color-text-warning: var(--primitive-warning-700);
  --color-text-error: var(--primitive-error-700);
  --color-text-info: var(--primitive-info-700);

  /* Surface Colors - Background Hierarchy */
  --color-surface-primary: var(--primitive-neutral-0);
  --color-surface-secondary: var(--primitive-neutral-25);
  --color-surface-tertiary: var(--primitive-neutral-50);
  --color-surface-quaternary: var(--primitive-neutral-100);
  --color-surface-elevated: var(--primitive-neutral-0);
  --color-surface-overlay: rgba(255, 255, 255, 0.95);
  --color-surface-backdrop: rgba(15, 23, 42, 0.8);
  --color-surface-brand: var(--primitive-primary-600);
  --color-surface-brand-subtle: var(--primitive-primary-50);
  --color-surface-success: var(--primitive-success-50);
  --color-surface-warning: var(--primitive-warning-50);
  --color-surface-error: var(--primitive-error-50);
  --color-surface-info: var(--primitive-info-50);

  /* Border Colors - Structural Elements */
  --color-border-primary: var(--primitive-neutral-200);
  --color-border-secondary: var(--primitive-neutral-100);
  --color-border-tertiary: var(--primitive-neutral-50);
  --color-border-brand: var(--primitive-primary-300);
  --color-border-brand-strong: var(--primitive-primary-600);
  --color-border-success: var(--primitive-success-300);
  --color-border-warning: var(--primitive-warning-300);
  --color-border-error: var(--primitive-error-300);
  --color-border-info: var(--primitive-info-300);
  --color-border-focus: var(--primitive-primary-500);
  --color-border-disabled: var(--primitive-neutral-200);

  /* Interactive Colors - User Actions */
  --color-interactive-primary: var(--primitive-primary-600);
  --color-interactive-primary-hover: var(--primitive-primary-700);
  --color-interactive-primary-active: var(--primitive-primary-800);
  --color-interactive-primary-disabled: var(--primitive-neutral-300);
  --color-interactive-secondary: var(--primitive-neutral-0);
  --color-interactive-secondary-hover: var(--primitive-neutral-50);
  --color-interactive-secondary-active: var(--primitive-neutral-100);
  --color-interactive-tertiary: transparent;
  --color-interactive-tertiary-hover: var(--primitive-neutral-50);
  --color-interactive-tertiary-active: var(--primitive-neutral-100);

  /* Legacy Aliases for Backward Compatibility */
  --color-primary: var(--color-text-primary);
  --color-secondary: var(--color-text-secondary);
  --color-tertiary: var(--color-text-tertiary);
  --color-accent: var(--color-brand-primary);
  --color-accent-hover: var(--color-brand-primary-hover);
  --color-accent-active: var(--color-brand-primary-active);
  --color-success: var(--primitive-success-600);
  --color-warning: var(--primitive-warning-600);
  --color-error: var(--primitive-error-600);
  --color-muted: var(--color-text-quaternary);

  /* ===== TYPOGRAPHY TOKENS ===== */

  /* Font Families - Professional Typography Stack */
  --font-family-sans: 'Inter Variable', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'JetBrains Mono Variable', 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, monospace;
  --font-family-display: var(--font-family-sans);

  /* Font Weights - Variable Font Support */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* Font Size Scale - Modular Scale (1.25 ratio) */
  --font-size-2xs: 0.625rem;   /* 10px */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */
  --font-size-7xl: 4.5rem;     /* 72px */
  --font-size-8xl: 6rem;       /* 96px */
  --font-size-9xl: 8rem;       /* 128px */

  /* Line Heights - Optimized for Different Content Types */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Letter Spacing - Subtle Adjustments for Readability */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Typography Semantic Tokens */
  --typography-display-1-size: var(--font-size-6xl);
  --typography-display-1-weight: var(--font-weight-bold);
  --typography-display-1-line-height: var(--line-height-tight);
  --typography-display-1-letter-spacing: var(--letter-spacing-tight);

  --typography-display-2-size: var(--font-size-5xl);
  --typography-display-2-weight: var(--font-weight-bold);
  --typography-display-2-line-height: var(--line-height-tight);
  --typography-display-2-letter-spacing: var(--letter-spacing-tight);

  --typography-heading-1-size: var(--font-size-4xl);
  --typography-heading-1-weight: var(--font-weight-semibold);
  --typography-heading-1-line-height: var(--line-height-tight);
  --typography-heading-1-letter-spacing: var(--letter-spacing-tight);

  --typography-heading-2-size: var(--font-size-3xl);
  --typography-heading-2-weight: var(--font-weight-semibold);
  --typography-heading-2-line-height: var(--line-height-snug);
  --typography-heading-2-letter-spacing: var(--letter-spacing-normal);

  --typography-heading-3-size: var(--font-size-2xl);
  --typography-heading-3-weight: var(--font-weight-semibold);
  --typography-heading-3-line-height: var(--line-height-snug);
  --typography-heading-3-letter-spacing: var(--letter-spacing-normal);

  --typography-heading-4-size: var(--font-size-xl);
  --typography-heading-4-weight: var(--font-weight-medium);
  --typography-heading-4-line-height: var(--line-height-normal);
  --typography-heading-4-letter-spacing: var(--letter-spacing-normal);

  --typography-body-large-size: var(--font-size-lg);
  --typography-body-large-weight: var(--font-weight-normal);
  --typography-body-large-line-height: var(--line-height-relaxed);
  --typography-body-large-letter-spacing: var(--letter-spacing-normal);

  --typography-body-size: var(--font-size-base);
  --typography-body-weight: var(--font-weight-normal);
  --typography-body-line-height: var(--line-height-relaxed);
  --typography-body-letter-spacing: var(--letter-spacing-normal);

  --typography-body-small-size: var(--font-size-sm);
  --typography-body-small-weight: var(--font-weight-normal);
  --typography-body-small-line-height: var(--line-height-normal);
  --typography-body-small-letter-spacing: var(--letter-spacing-normal);

  --typography-caption-size: var(--font-size-xs);
  --typography-caption-weight: var(--font-weight-medium);
  --typography-caption-line-height: var(--line-height-normal);
  --typography-caption-letter-spacing: var(--letter-spacing-wide);

  /* ===== SPACING TOKENS ===== */

  /* Base Spacing Scale - 4px Grid System */
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 0.125rem;  /* 2px */
  --space-1: 0.25rem;     /* 4px */
  --space-1-5: 0.375rem;  /* 6px */
  --space-2: 0.5rem;      /* 8px */
  --space-2-5: 0.625rem;  /* 10px */
  --space-3: 0.75rem;     /* 12px */
  --space-3-5: 0.875rem;  /* 14px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-7: 1.75rem;     /* 28px */
  --space-8: 2rem;        /* 32px */
  --space-9: 2.25rem;     /* 36px */
  --space-10: 2.5rem;     /* 40px */
  --space-11: 2.75rem;    /* 44px */
  --space-12: 3rem;       /* 48px */
  --space-14: 3.5rem;     /* 56px */
  --space-16: 4rem;       /* 64px */
  --space-20: 5rem;       /* 80px */
  --space-24: 6rem;       /* 96px */
  --space-28: 7rem;       /* 112px */
  --space-32: 8rem;       /* 128px */
  --space-36: 9rem;       /* 144px */
  --space-40: 10rem;      /* 160px */
  --space-44: 11rem;      /* 176px */
  --space-48: 12rem;      /* 192px */
  --space-52: 13rem;      /* 208px */
  --space-56: 14rem;      /* 224px */
  --space-60: 15rem;      /* 240px */
  --space-64: 16rem;      /* 256px */
  --space-72: 18rem;      /* 288px */
  --space-80: 20rem;      /* 320px */
  --space-96: 24rem;      /* 384px */

  /* Semantic Spacing Tokens */
  --spacing-component-padding-xs: var(--space-2);
  --spacing-component-padding-sm: var(--space-3);
  --spacing-component-padding-md: var(--space-4);
  --spacing-component-padding-lg: var(--space-6);
  --spacing-component-padding-xl: var(--space-8);

  --spacing-component-gap-xs: var(--space-1);
  --spacing-component-gap-sm: var(--space-2);
  --spacing-component-gap-md: var(--space-4);
  --spacing-component-gap-lg: var(--space-6);
  --spacing-component-gap-xl: var(--space-8);

  --spacing-layout-section-xs: var(--space-8);
  --spacing-layout-section-sm: var(--space-12);
  --spacing-layout-section-md: var(--space-16);
  --spacing-layout-section-lg: var(--space-20);
  --spacing-layout-section-xl: var(--space-24);

  --spacing-layout-container: var(--space-4);
  --spacing-layout-container-sm: var(--space-6);
  --spacing-layout-container-lg: var(--space-8);

  /* Legacy Aliases */
  --space-card-padding: var(--spacing-component-padding-lg);
  --space-section-padding: var(--spacing-layout-section-md);
  --space-container-padding: var(--spacing-layout-container);

  /* ===== ELEVATION TOKENS (SHADOWS) ===== */

  /* Shadow Scale - Layered Depth System */
  --elevation-none: none;
  --elevation-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --elevation-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --elevation-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --elevation-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --elevation-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --elevation-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --elevation-3xl: 0 35px 60px -12px rgba(0, 0, 0, 0.35);

  /* Specialized Shadows */
  --elevation-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  --elevation-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
  --elevation-focus-error: 0 0 0 3px rgba(239, 68, 68, 0.1);
  --elevation-focus-success: 0 0 0 3px rgba(16, 185, 129, 0.1);

  /* Semantic Elevation Tokens */
  --elevation-card: var(--elevation-sm);
  --elevation-card-hover: var(--elevation-md);
  --elevation-card-active: var(--elevation-lg);
  --elevation-dropdown: var(--elevation-lg);
  --elevation-modal: var(--elevation-2xl);
  --elevation-tooltip: var(--elevation-md);
  --elevation-popover: var(--elevation-lg);

  /* Legacy Aliases */
  --shadow-xs: var(--elevation-xs);
  --shadow-sm: var(--elevation-sm);
  --shadow-md: var(--elevation-md);
  --shadow-lg: var(--elevation-lg);
  --shadow-xl: var(--elevation-xl);
  --shadow-2xl: var(--elevation-2xl);
  --shadow-inner: var(--elevation-inner);
  --shadow-card: var(--elevation-card);
  --shadow-card-hover: var(--elevation-card-hover);
  --shadow-card-active: var(--elevation-card-active);

  /* ===== BORDER RADIUS TOKENS ===== */

  /* Radius Scale - Consistent Rounding */
  --radius-none: 0;
  --radius-xs: 0.125rem;  /* 2px */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;

  /* Semantic Radius Tokens */
  --radius-button: var(--radius-md);
  --radius-input: var(--radius-md);
  --radius-card: var(--radius-lg);
  --radius-modal: var(--radius-xl);
  --radius-badge: var(--radius-full);
  --radius-avatar: var(--radius-full);

  /* ===== ANIMATION TOKENS ===== */

  /* Transition Durations */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  --duration-slowest: 750ms;

  /* Easing Functions - Professional Motion */
  --easing-linear: linear;
  --easing-ease: ease;
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Semantic Transition Tokens */
  --transition-fast: var(--duration-fast) var(--easing-ease-out);
  --transition-normal: var(--duration-normal) var(--easing-ease-out);
  --transition-slow: var(--duration-slow) var(--easing-ease-out);
  --transition-bounce: var(--duration-slower) var(--easing-bounce);
  --transition-spring: var(--duration-normal) var(--easing-spring);

  /* ===== Z-INDEX TOKENS ===== */

  /* Layer System - Organized Stacking */
  --z-base: 0;
  --z-docked: 10;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-banner: 1030;
  --z-overlay: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-skip-link: 1070;
  --z-toast: 1080;
  --z-tooltip: 1090;

  /* Legacy Aliases */
  --z-fixed: var(--z-sticky);
  --z-modal-backdrop: var(--z-overlay);

  /* ===== BREAKPOINT TOKENS ===== */

  /* Responsive Breakpoints */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Container Max Widths */
  --container-xs: 475px;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* ===== DARK THEME OVERRIDES ===== */
[data-theme="dark"] {
  /* Text Colors - High Contrast Dark Mode */
  --color-text-primary: var(--primitive-neutral-50);
  --color-text-secondary: var(--primitive-neutral-200);
  --color-text-tertiary: var(--primitive-neutral-400);
  --color-text-quaternary: var(--primitive-neutral-500);
  --color-text-disabled: var(--primitive-neutral-600);
  --color-text-inverse: var(--primitive-neutral-900);
  --color-text-brand: var(--primitive-primary-400);
  --color-text-success: var(--primitive-success-400);
  --color-text-warning: var(--primitive-warning-400);
  --color-text-error: var(--primitive-error-400);
  --color-text-info: var(--primitive-info-400);

  /* Surface Colors - Professional Dark Mode */
  --color-surface-primary: var(--primitive-neutral-900);
  --color-surface-secondary: var(--primitive-neutral-800);
  --color-surface-tertiary: var(--primitive-neutral-700);
  --color-surface-quaternary: var(--primitive-neutral-600);
  --color-surface-elevated: var(--primitive-neutral-800);
  --color-surface-overlay: rgba(15, 23, 42, 0.95);
  --color-surface-backdrop: rgba(0, 0, 0, 0.8);
  --color-surface-brand: var(--primitive-primary-600);
  --color-surface-brand-subtle: var(--primitive-primary-900);
  --color-surface-success: var(--primitive-success-900);
  --color-surface-warning: var(--primitive-warning-900);
  --color-surface-error: var(--primitive-error-900);
  --color-surface-info: var(--primitive-info-900);

  /* Border Colors - Subtle Dark Mode */
  --color-border-primary: var(--primitive-neutral-700);
  --color-border-secondary: var(--primitive-neutral-800);
  --color-border-tertiary: var(--primitive-neutral-850);
  --color-border-brand: var(--primitive-primary-600);
  --color-border-brand-strong: var(--primitive-primary-500);
  --color-border-success: var(--primitive-success-600);
  --color-border-warning: var(--primitive-warning-600);
  --color-border-error: var(--primitive-error-600);
  --color-border-info: var(--primitive-info-600);
  --color-border-focus: var(--primitive-primary-400);
  --color-border-disabled: var(--primitive-neutral-700);

  /* Interactive Colors - Dark Mode Adjustments */
  --color-interactive-primary: var(--primitive-primary-500);
  --color-interactive-primary-hover: var(--primitive-primary-400);
  --color-interactive-primary-active: var(--primitive-primary-300);
  --color-interactive-primary-disabled: var(--primitive-neutral-600);
  --color-interactive-secondary: var(--primitive-neutral-800);
  --color-interactive-secondary-hover: var(--primitive-neutral-700);
  --color-interactive-secondary-active: var(--primitive-neutral-600);
  --color-interactive-tertiary: transparent;
  --color-interactive-tertiary-hover: var(--primitive-neutral-800);
  --color-interactive-tertiary-active: var(--primitive-neutral-700);

  /* Enhanced Shadows for Dark Mode */
  --elevation-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --elevation-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --elevation-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --elevation-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --elevation-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
  --elevation-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --elevation-3xl: 0 35px 60px -12px rgba(0, 0, 0, 0.7);
  --elevation-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
  --elevation-focus: 0 0 0 3px rgba(96, 165, 250, 0.2);

  /* Legacy Aliases Updates */
  --color-primary: var(--color-text-primary);
  --color-secondary: var(--color-text-secondary);
  --color-tertiary: var(--color-text-tertiary);
  --color-accent: var(--color-brand-primary);
  --color-accent-hover: var(--color-brand-primary-hover);
  --color-accent-active: var(--color-brand-primary-active);
  --color-muted: var(--color-text-quaternary);

  /* Shadow Legacy Aliases */
  --shadow-xs: var(--elevation-xs);
  --shadow-sm: var(--elevation-sm);
  --shadow-md: var(--elevation-md);
  --shadow-lg: var(--elevation-lg);
  --shadow-xl: var(--elevation-xl);
  --shadow-2xl: var(--elevation-2xl);
  --shadow-inner: var(--elevation-inner);
  --shadow-card: var(--elevation-md);
  --shadow-card-hover: var(--elevation-lg);
  --shadow-card-active: var(--elevation-xl);
}

/* Container Query Support */
@container (min-width: 320px) {
  .container-responsive {
    --space-container: var(--space-fluid-xs);
  }
}

@container (min-width: 640px) {
  .container-responsive {
    --space-container: var(--space-fluid-sm);
  }
}

@container (min-width: 768px) {
  .container-responsive {
    --space-container: var(--space-fluid-md);
  }
}

@container (min-width: 1024px) {
  .container-responsive {
    --space-container: var(--space-fluid-lg);
  }
}

@container (min-width: 1280px) {
  .container-responsive {
    --space-container: var(--space-fluid-xl);
  }
}
/* ===== BASE LAYER ENHANCEMENTS ===== */

@layer base {
  /* Professional Typography Foundation */
  html {
    font-family: var(--font-family-sans);
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    background-color: var(--color-surface-primary);
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    transition: background-color var(--transition-normal), color var(--transition-normal);
  }

  /* Professional Focus Styles - Enterprise Standards */
  *:focus-visible {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }

  /* Smooth Theme Transitions */
  * {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: var(--transition-fast);
  }

  /* Typography Hierarchy */
  h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    line-height: var(--line-height-tight);
  }

  h1 { font-size: var(--font-size-4xl); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }
  h4 { font-size: var(--font-size-xl); }
  h5 { font-size: var(--font-size-lg); }
  h6 { font-size: var(--font-size-base); }

  p {
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
  }

  code {
    font-family: var(--font-family-mono);
    font-size: 0.875em;
    background-color: var(--color-surface-tertiary);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
  }
}

/* ===== COMPONENT LAYER ===== */

@layer components {
  /* ===== PROFESSIONAL CARD SYSTEM ===== */

  /* ===== ENHANCED CARD SYSTEM ===== */

  /* Base Card Component */
  .card {
    background-color: var(--color-surface-elevated);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-card);
    box-shadow: var(--elevation-card);
    padding: var(--spacing-component-padding-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
  }

  .card:hover {
    box-shadow: var(--elevation-card-hover);
    transform: translateY(-1px);
  }

  /* Card Variants */
  .card-interactive {
    cursor: pointer;
  }

  .card-interactive:hover {
    box-shadow: var(--elevation-card-active);
    transform: translateY(-2px);
  }

  .card-interactive:active {
    transform: translateY(0);
    box-shadow: var(--elevation-card);
  }

  .card-interactive:focus-visible {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
  }

  .card-elevated {
    box-shadow: var(--elevation-lg);
  }

  .card-elevated:hover {
    box-shadow: var(--elevation-xl);
  }

  .card-flat {
    box-shadow: var(--elevation-none);
    border: 1px solid var(--color-border-secondary);
  }

  .card-outlined {
    background-color: transparent;
    border: 2px solid var(--color-border-primary);
    box-shadow: var(--elevation-none);
  }

  .card-ghost {
    background-color: transparent;
    border: none;
    box-shadow: var(--elevation-none);
  }

  .card-ghost:hover {
    background-color: var(--color-surface-tertiary);
  }

  /* Card Sizes */
  .card-sm {
    padding: var(--spacing-component-padding-sm);
  }

  .card-md {
    padding: var(--spacing-component-padding-md);
  }

  .card-lg {
    padding: var(--spacing-component-padding-lg);
  }

  .card-xl {
    padding: var(--spacing-component-padding-xl);
  }

  /* Card Content Structure */
  .card-header {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--color-border-secondary);
  }

  .card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0;
  }

  .card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-tertiary);
    margin: var(--space-1) 0 0 0;
  }

  .card-body {
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
  }

  .card-footer {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--color-border-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  /* Grid System for Cards */
  .card-grid {
    display: grid;
    gap: var(--space-6);
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }

  .card-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  .card-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  @media (max-width: 768px) {
    .card-grid,
    .card-grid-2,
    .card-grid-3 {
      grid-template-columns: 1fr;
    }
  }

  /* ===== PROFESSIONAL BUTTON SYSTEM ===== */

  /* ===== ENHANCED BUTTON SYSTEM ===== */

  /* Base Button Styles */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-component-gap-sm);
    padding: var(--spacing-component-padding-sm) var(--spacing-component-padding-md);
    font-family: var(--font-family-sans);
    font-size: var(--typography-body-small-size);
    font-weight: var(--typography-body-small-weight);
    line-height: var(--typography-body-small-line-height);
    border-radius: var(--radius-button);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
  }

  .btn:focus-visible {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
  }

  .btn:disabled,
  .btn[aria-disabled="true"] {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
  }

  /* Button Variants */
  .btn-primary {
    background-color: var(--color-interactive-primary);
    color: var(--color-text-inverse);
    border-color: var(--color-interactive-primary);
    box-shadow: var(--elevation-xs);
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--color-interactive-primary-hover);
    border-color: var(--color-interactive-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--elevation-sm);
  }

  .btn-primary:active:not(:disabled) {
    background-color: var(--color-interactive-primary-active);
    border-color: var(--color-interactive-primary-active);
    transform: translateY(0);
    box-shadow: var(--elevation-xs);
  }

  .btn-secondary {
    background-color: var(--color-interactive-secondary);
    color: var(--color-text-primary);
    border-color: var(--color-border-primary);
    box-shadow: var(--elevation-xs);
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: var(--color-interactive-secondary-hover);
    border-color: var(--color-border-brand);
    transform: translateY(-1px);
    box-shadow: var(--elevation-sm);
  }

  .btn-secondary:active:not(:disabled) {
    background-color: var(--color-interactive-secondary-active);
    transform: translateY(0);
    box-shadow: var(--elevation-xs);
  }

  .btn-outline {
    background-color: var(--color-interactive-tertiary);
    color: var(--color-text-brand);
    border-color: var(--color-border-brand);
  }

  .btn-outline:hover:not(:disabled) {
    background-color: var(--color-interactive-primary);
    color: var(--color-text-inverse);
    border-color: var(--color-interactive-primary);
  }

  .btn-outline:active:not(:disabled) {
    background-color: var(--color-interactive-primary-active);
    border-color: var(--color-interactive-primary-active);
  }

  .btn-ghost {
    background-color: var(--color-interactive-tertiary);
    color: var(--color-text-secondary);
    border-color: transparent;
  }

  .btn-ghost:hover:not(:disabled) {
    background-color: var(--color-interactive-tertiary-hover);
    color: var(--color-text-primary);
  }

  .btn-ghost:active:not(:disabled) {
    background-color: var(--color-interactive-tertiary-active);
  }

  .btn-destructive {
    background-color: var(--primitive-error-600);
    color: var(--color-text-inverse);
    border-color: var(--primitive-error-600);
  }

  .btn-destructive:hover:not(:disabled) {
    background-color: var(--primitive-error-700);
    border-color: var(--primitive-error-700);
  }

  /* Button Sizes */
  .btn-xs {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-2xs);
    gap: var(--space-1);
  }

  .btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
    gap: var(--space-1-5);
  }

  .btn-md {
    padding: var(--space-2-5) var(--space-4);
    font-size: var(--font-size-sm);
    gap: var(--space-2);
  }

  .btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    gap: var(--space-2);
  }

  .btn-xl {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
    gap: var(--space-2-5);
  }

  /* Button States */
  .btn-loading {
    color: transparent;
  }

  .btn-loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* ===== PROFESSIONAL FORM SYSTEM ===== */

  /* Form Groups */
  .form-group {
    margin-bottom: var(--space-4);
  }

  .form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    margin-bottom: var(--space-2);
  }

  .form-label.required::after {
    content: ' *';
    color: var(--color-error);
  }

  /* Input Fields */
  .form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--color-text-primary);
    background-color: var(--color-surface-elevated);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-input);
    transition: all var(--transition-normal);
  }

  .form-input:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: var(--color-surface-primary);
  }

  .form-input::placeholder {
    color: var(--color-text-muted);
  }

  .form-input:disabled {
    background-color: var(--color-surface-tertiary);
    color: var(--color-text-muted);
    cursor: not-allowed;
  }

  /* Input States */
  .form-input.error {
    border-color: var(--color-error);
  }

  .form-input.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  .form-input.success {
    border-color: var(--color-success);
  }

  /* Form Feedback */
  .form-feedback {
    margin-top: var(--space-1);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
  }

  .form-feedback.error {
    color: var(--color-error);
  }

  .form-feedback.success {
    color: var(--color-success);
  }

  .form-feedback.help {
    color: var(--color-text-tertiary);
  }

  /* Select Fields */
  .form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  /* Checkbox and Radio */
  .form-checkbox,
  .form-radio {
    width: 1rem;
    height: 1rem;
    margin-right: var(--space-2);
    accent-color: var(--color-accent);
  }

  /* ===== LAYOUT UTILITIES ===== */

  /* Container System */
  .container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-container-padding);
  }

  .container-sm {
    max-width: 640px;
  }

  .container-md {
    max-width: 768px;
  }

  .container-lg {
    max-width: 1024px;
  }

  .container-xl {
    max-width: 1280px;
  }

  /* Section Spacing */
  .section {
    padding: var(--space-section-padding) 0;
  }

  .section-sm {
    padding: var(--space-12) 0;
  }

  .section-lg {
    padding: var(--space-24) 0;
  }

  /* Flex Utilities */
  .flex {
    display: flex;
  }

  .flex-col {
    flex-direction: column;
  }

  .items-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .gap-2 { gap: var(--space-2); }
  .gap-3 { gap: var(--space-3); }
  .gap-4 { gap: var(--space-4); }
  .gap-6 { gap: var(--space-6); }
  .gap-8 { gap: var(--space-8); }

  /* ===== TYPOGRAPHY UTILITIES ===== */

  /* Text Sizes */
  .text-xs { font-size: var(--font-size-xs); }
  .text-sm { font-size: var(--font-size-sm); }
  .text-base { font-size: var(--font-size-base); }
  .text-lg { font-size: var(--font-size-lg); }
  .text-xl { font-size: var(--font-size-xl); }
  .text-2xl { font-size: var(--font-size-2xl); }
  .text-3xl { font-size: var(--font-size-3xl); }
  .text-4xl { font-size: var(--font-size-4xl); }

  /* Font Weights */
  .font-light { font-weight: var(--font-weight-light); }
  .font-normal { font-weight: var(--font-weight-normal); }
  .font-medium { font-weight: var(--font-weight-medium); }
  .font-semibold { font-weight: var(--font-weight-semibold); }
  .font-bold { font-weight: var(--font-weight-bold); }

  /* Text Colors */
  .text-primary { color: var(--color-text-primary); }
  .text-secondary { color: var(--color-text-secondary); }
  .text-tertiary { color: var(--color-text-tertiary); }
  .text-accent { color: var(--color-text-accent); }
  .text-muted { color: var(--color-text-muted); }
  .text-inverse { color: var(--color-text-inverse); }
  .text-success { color: var(--color-success); }
  .text-warning { color: var(--color-warning); }
  .text-error { color: var(--color-error); }

  /* Text Alignment */
  .text-left { text-align: left; }
  .text-center { text-align: center; }
  .text-right { text-align: right; }

  /* Line Heights */
  .leading-tight { line-height: var(--line-height-tight); }
  .leading-snug { line-height: var(--line-height-snug); }
  .leading-normal { line-height: var(--line-height-normal); }
  .leading-relaxed { line-height: var(--line-height-relaxed); }

  /* ===== SPACING UTILITIES ===== */

  /* Margin */
  .m-0 { margin: var(--space-0); }
  .m-1 { margin: var(--space-1); }
  .m-2 { margin: var(--space-2); }
  .m-3 { margin: var(--space-3); }
  .m-4 { margin: var(--space-4); }
  .m-6 { margin: var(--space-6); }
  .m-8 { margin: var(--space-8); }

  .mt-0 { margin-top: var(--space-0); }
  .mt-2 { margin-top: var(--space-2); }
  .mt-4 { margin-top: var(--space-4); }
  .mt-6 { margin-top: var(--space-6); }
  .mt-8 { margin-top: var(--space-8); }

  .mb-0 { margin-bottom: var(--space-0); }
  .mb-2 { margin-bottom: var(--space-2); }
  .mb-4 { margin-bottom: var(--space-4); }
  .mb-6 { margin-bottom: var(--space-6); }
  .mb-8 { margin-bottom: var(--space-8); }

  /* Padding */
  .p-0 { padding: var(--space-0); }
  .p-2 { padding: var(--space-2); }
  .p-4 { padding: var(--space-4); }
  .p-6 { padding: var(--space-6); }
  .p-8 { padding: var(--space-8); }

  .px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
  .px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
  .py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
  .py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
  .py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }

  /* ===== STATUS INDICATORS ===== */

  /* Badge Component */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-full);
    white-space: nowrap;
  }

  .badge-primary {
    background-color: var(--color-blue-100);
    color: var(--color-blue-700);
  }

  .badge-success {
    background-color: var(--color-emerald-100);
    color: var(--color-emerald-700);
  }

  .badge-warning {
    background-color: var(--color-amber-100);
    color: var(--color-amber-700);
  }

  .badge-error {
    background-color: var(--color-red-100);
    color: var(--color-red-700);
  }

  .badge-neutral {
    background-color: var(--color-slate-100);
    color: var(--color-slate-700);
  }

  /* Status Dots */
  .status-dot {
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: var(--space-2);
  }

  .status-dot.online {
    background-color: var(--color-success);
  }

  .status-dot.offline {
    background-color: var(--color-error);
  }

  .status-dot.away {
    background-color: var(--color-warning);
  }

  /* ===== ANIMATIONS ===== */

  /* Fade In Animation */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  /* Loading Spinner */
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  /* Pulse Animation */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Hover Lift Effect */
  .hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
  /* ===== ACCESSIBILITY UTILITIES ===== */

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus-visible-only:not(:focus-visible) {
    outline: none;
  }

  /* ===== RESPONSIVE UTILITIES ===== */

  .hidden { display: none; }
  .block { display: block; }
  .inline-block { display: inline-block; }
  .inline { display: inline; }

  @media (max-width: 640px) {
    .sm\\:hidden { display: none; }
    .sm\\:block { display: block; }
  }

  @media (max-width: 768px) {
    .md\\:hidden { display: none; }
    .md\\:block { display: block; }
  }

  @media (max-width: 1024px) {
    .lg\\:hidden { display: none; }
    .lg\\:block { display: block; }
  }




}

/* ===== UTILITIES LAYER ===== */

@layer utilities {
  /* Theme Toggle Utilities */
  .theme-transition {
    transition: background-color var(--transition-normal),
                color var(--transition-normal),
                border-color var(--transition-normal);
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .card {
      border-width: 2px;
    }

    .btn {
      border-width: 2px;
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .hover-lift:hover {
      transform: none !important;
    }
  }
}

/* ===== KEYFRAME ANIMATIONS ===== */

/* Animations are defined in the components layer above */

/* ===== PRINT STYLES ===== */

@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}