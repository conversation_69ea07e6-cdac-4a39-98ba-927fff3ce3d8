import React, { InputHTMLAttributes } from 'react';
import { designUtils } from '../../lib/designSystem';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  icon,
  fullWidth = true,
  className = '',
  ...props
}) => {
  const widthClass = fullWidth ? 'w-full' : '';
  
  return (
    <div className={designUtils.cn(widthClass, className)}>
      {label && (
        <label className="block text-sm font-medium text-secondary-600 dark:text-secondary-300 mb-2">
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-tertiary">
            {icon}
          </div>
        )}
        <input
          className={designUtils.cn(
            'input-field',
            icon ? 'pl-10' : '',
            error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''
          )}
          {...props}
        />
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
};

export default Input;
