import React from 'react';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  bgClass?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  bgClass = 'bg-gradient-to-r from-primary-600 to-secondary-600',
}) => {
  return (
    <div className={`py-16 sm:py-24 ${bgClass} text-white`}>
      {/* Decorative elements */}
      <div className="absolute top-20 right-0 -mr-20 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
      <div className="absolute top-40 left-0 -ml-20 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
            {title}
          </h1>
          {subtitle && (
            <p className="mt-6 max-w-3xl mx-auto text-xl text-white/90">
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PageHeader; 