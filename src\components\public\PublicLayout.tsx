import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Shield, Menu, X } from 'lucide-react';
import LanguageSelector from '../LanguageSelector';
import FooterLanguageSelector from '../FooterLanguageSelector';
import UserAuthButton from '../ui/UserAuthButton';
import { useAuth } from '../../hooks/useAuth';
import SEO from '../SEO';

interface PublicLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

const PublicLayout: React.FC<PublicLayoutProps> = ({ children, title, description }) => {
  const { t } = useTranslation(['common', 'public', 'auth']);
  const location = useLocation();
  const currentPath = location.pathname;
  const { user } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Determine page title based on current path if not provided
  const pageTitle = title || (() => {
    switch (currentPath) {
      case '/':
        return t('public:heroTitle1');
      case '/features':
        return t('public:features');
      case '/pricing':
        return t('public:pricing');
      case '/changelog':
        return t('public:changelog');
      case '/contact':
        return t('public:contact');
      case '/help':
        return t('public:help');
      case '/privacy':
        return t('public:privacy');
      case '/terms':
        return t('public:terms');
      default:
        return '';
    }
  })();

  return (
    <div className="min-h-screen bg-dark-900 text-white">
      <SEO title={pageTitle} description={description} />

      {/* Éléments décoratifs pixelisés */}
      <div className="fixed top-20 right-10 w-32 h-32 opacity-20 z-0">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:10px_10px] animate-pulse-slow"></div>
      </div>
      <div className="fixed bottom-20 left-10 w-40 h-40 opacity-20 z-0">
        <div className="absolute w-full h-full bg-pixel-pattern bg-[size:15px_15px] animate-pulse-slow"></div>
      </div>

      <header className="bg-dark-800 border-b border-dark-700 sticky top-0 z-50">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-20 flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 flex-shrink-0">
            <div className="bg-primary-500 p-2 rounded-lg shadow-neon">
              <Shield className="h-6 w-6 text-dark-900" />
            </div>
            <span className="text-xl font-bold font-mono text-white">
              {t('common:appName')}
            </span>
          </Link>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2 rounded-md text-primary-500 hover:text-primary-400 focus:outline-none"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/features"
              className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
            >
              {t('public:features')}
            </Link>
            <Link
              to="/pricing"
              className={`text-primary-400 hover:text-primary-300 px-3 py-2 text-sm font-medium ${
                currentPath === '/pricing' ? 'text-primary-300 font-semibold' : ''
              }`}
            >
              {t('public:pricing')}
            </Link>
            <Link
              to="/changelog"
              className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
            >
              {t('public:changelog')}
            </Link>
            <Link
              to="/contact"
              className={`text-primary-400 hover:text-primary-300 px-3 py-2 text-sm font-medium ${
                currentPath === '/contact' ? 'text-primary-300 font-semibold' : ''
              }`}
            >
              {t('public:contact')}
            </Link>
          </div>

          {/* Desktop auth buttons and language switcher */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSelector />
            <UserAuthButton variant="header" />
          </div>
        </nav>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-dark-800 border-t border-dark-700 py-5 px-4 shadow-xl z-40">
            <div className="flex flex-col space-y-5">
              <Link
                to="/features"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                {t('public:features')}
              </Link>
              <Link
                to="/pricing"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                {t('public:pricing')}
              </Link>
              <Link
                to="/changelog"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                {t('public:changelog')}
              </Link>
              <Link
                to="/contact"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                {t('public:contact')}
              </Link>
              <hr className="border-dark-700" />
              <LanguageSelector />
              <UserAuthButton
                variant="mobile"
                onActionComplete={() => setMobileMenuOpen(false)}
              />
            </div>
          </div>
        )}
      </header>

      <main className="flex-grow relative z-1">
        {children}
      </main>

      <footer className="mt-16 py-12 bg-dark-800 border-t border-dark-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="bg-primary-500 p-2 rounded-lg shadow-neon">
                  <Shield className="h-6 w-6 text-dark-900" />
                </div>
                <span className="text-xl font-bold font-mono text-white">
                  {t('common:appName')}
                </span>
              </div>
              <p className="text-dark-300 text-sm">
                {t('public:footerTagline')}
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">{t('public:product')}</h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/features" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:features')}
                  </Link>
                </li>
                <li>
                  <Link to="/pricing" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:pricing')}
                  </Link>
                </li>
                <li>
                  <Link to="/changelog" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:changelog')}
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">{t('public:company')}</h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/contact" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:contact')}
                  </Link>
                </li>
                <li>
                  <Link to="/legal" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:legal')}
                  </Link>
                </li>
                <li>
                  <Link to="/privacy" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:privacy')}
                  </Link>
                </li>
                <li>
                  <Link to="/terms" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:terms')}
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">{t('public:help')}</h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/help" className="text-dark-300 hover:text-white transition-colors">
                    {t('public:helpCenter')}
                  </Link>
                </li>
                <li>
                  <a href="mailto:<EMAIL>" className="text-dark-300 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-8 border-t border-dark-700 flex flex-col items-center justify-center space-y-4">
            <div className="language-selector-footer">
              <FooterLanguageSelector />
            </div>
            <p className="text-center text-sm text-dark-400">© {new Date().getFullYear()} Cloud OTP. {t('common:footerText')}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicLayout;