/**
 * Service de validation des mots de passe secondaires
 * Implémente des règles strictes pour garantir la sécurité du chiffrement
 */

// Constantes pour les exigences de mot de passe
export const PASSWORD_MIN_LENGTH = 8; // Réduit à 8 caractères selon la demande du client
export const PASSWORD_REQUIRE_UPPERCASE = true;
export const PASSWORD_REQUIRE_LOWERCASE = true;
export const PASSWORD_REQUIRE_NUMBERS = true;
export const PASSWORD_REQUIRE_SPECIAL = true;
export const PASSWORD_MIN_ENTROPY = 50; // Bits d'entropie minimale ajustée pour la longueur réduite

/**
 * Vérifie si un mot de passe répond aux exigences de sécurité
 * @param password Le mot de passe à vérifier
 * @returns Un objet contenant le résultat de la validation et les messages d'erreur
 */
export function validateSecondaryPassword(password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong' | 'very-strong';
  entropy: number;
} {
  const errors: string[] = [];

  // Vérifier la longueur minimale
  if (password.length < PASSWORD_MIN_LENGTH) {
    errors.push(`Password must be at least ${PASSWORD_MIN_LENGTH} characters long`);
  }

  // Vérifier la présence de lettres majuscules
  if (PASSWORD_REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  // Vérifier la présence de lettres minuscules
  if (PASSWORD_REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  // Vérifier la présence de chiffres
  if (PASSWORD_REQUIRE_NUMBERS && !/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  // Vérifier la présence de caractères spéciaux
  if (PASSWORD_REQUIRE_SPECIAL && !/[^A-Za-z0-9]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  // Vérifier les séquences répétitives
  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password must not contain repeating characters (e.g., "aaa")');
  }

  // Vérifier les séquences prévisibles
  if (/(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210)/i.test(password)) {
    errors.push('Password must not contain sequential characters (e.g., "abc", "123")');
  }

  // Calculer l'entropie du mot de passe
  const entropy = calculatePasswordEntropy(password);

  // Déterminer la force du mot de passe en fonction de l'entropie
  let strength: 'weak' | 'medium' | 'strong' | 'very-strong';
  if (entropy < 50) {
    strength = 'weak';
    if (errors.length === 0) {
      errors.push('Password is too weak');
    }
  } else if (entropy < 70) {
    strength = 'medium';
  } else if (entropy < 90) {
    strength = 'strong';
  } else {
    strength = 'very-strong';
  }

  // Vérifier l'entropie minimale
  if (entropy < PASSWORD_MIN_ENTROPY) {
    errors.push(`Password is not complex enough (entropy: ${Math.round(entropy)} bits, required: ${PASSWORD_MIN_ENTROPY} bits)`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength,
    entropy
  };
}

/**
 * Calcule l'entropie d'un mot de passe en bits
 * L'entropie est une mesure de la force d'un mot de passe basée sur sa longueur et la taille du jeu de caractères
 * @param password Le mot de passe à évaluer
 * @returns L'entropie en bits
 */
function calculatePasswordEntropy(password: string): number {
  // Déterminer la taille du jeu de caractères
  let poolSize = 0;
  if (/[a-z]/.test(password)) poolSize += 26;
  if (/[A-Z]/.test(password)) poolSize += 26;
  if (/[0-9]/.test(password)) poolSize += 10;
  if (/[^A-Za-z0-9]/.test(password)) poolSize += 33; // Estimation pour les caractères spéciaux courants

  // Calculer l'entropie (formule : log2(poolSize^length))
  // Équivalent à : length * log2(poolSize)
  return password.length * (Math.log(poolSize) / Math.log(2));
}

/**
 * Génère un mot de passe aléatoire qui répond aux exigences de sécurité
 * @returns Un mot de passe fort généré aléatoirement
 */
export function generateStrongPassword(): string {
  const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
  const numberChars = '0123456789';
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;

  // Générer un mot de passe de longueur aléatoire entre MIN_LENGTH et MIN_LENGTH+4
  const length = PASSWORD_MIN_LENGTH + Math.floor(Math.random() * 4);

  // Commencer avec au moins un caractère de chaque type requis
  let password = '';
  password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
  password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
  password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
  password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));

  // Remplir le reste avec des caractères aléatoires
  for (let i = password.length; i < length; i++) {
    password += allChars.charAt(Math.floor(Math.random() * allChars.length));
  }

  // Mélanger le mot de passe pour éviter les motifs prévisibles
  password = password.split('').sort(() => 0.5 - Math.random()).join('');

  // Vérifier que le mot de passe généré répond aux exigences
  const validation = validateSecondaryPassword(password);
  if (!validation.isValid) {
    // Si le mot de passe généré n'est pas valide (cas rare), en générer un nouveau
    return generateStrongPassword();
  }

  return password;
}
