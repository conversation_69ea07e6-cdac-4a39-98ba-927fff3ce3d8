/**
 * Service pour la migration des données existantes vers le système chiffré
 */

import { supabase } from '../lib/supabase';
import * as encryptionService from './encryptionService';

/**
 * Migre les secrets OTP existants d'un utilisateur vers le format chiffré
 * @param userId ID de l'utilisateur
 * @param dataKey Clé de données pour le chiffrement
 * @returns Nombre de secrets migrés
 */
export async function migrateUserData(userId: string, dataKey: CryptoKey): Promise<{ migrated: number }> {
  // Démarrage de la migration des données

  // Récupérer tous les secrets OTP non chiffrés de l'utilisateur
  const { data: secrets, error } = await supabase
    .from('otp_secrets')
    .select('*')
    .eq('user_id', userId)
    .is('encrypted_secret', null); // Seulement les secrets qui n'ont pas encore été migrés

  if (error) {
    // Erreur lors de la récupération des secrets
    return { migrated: 0 };
  }

  if (!secrets || secrets.length === 0) {
    // Aucun secret à migrer
    return { migrated: 0 };
  }

  // Secrets trouvés pour la migration

  // Chiffrer chaque secret et préparer les mises à jour
  const updates = [];

  for (const secret of secrets) {
    try {
      // Vérifier que le secret existe et n'est pas déjà chiffré
      if (!secret.secret || secret.encrypted_secret) {
        continue;
      }

      // Chiffrer le secret
      const { ciphertext, iv } = await encryptionService.encrypt(secret.secret, dataKey);

      // Ajouter à la liste des mises à jour
      updates.push({
        id: secret.id,
        encrypted_secret: ciphertext,
        secret_iv: iv,
        // Garder le secret en clair temporairement pour la vérification
        secret: secret.secret
      });
    } catch (error) {
      console.error(`Failed to encrypt secret ${secret.id}:`, error);
    }
  }

  // Secrets préparés pour la mise à jour

  // Mettre à jour les secrets en batch
  if (updates.length > 0) {
    const { error: updateError } = await supabase
      .from('otp_secrets')
      .upsert(updates);

    if (updateError) {
      // Erreur lors de la mise à jour des secrets chiffrés
      throw new Error(`Failed to update encrypted secrets: ${updateError.message}`);
    }

    // Migration réussie
  }

  return { migrated: updates.length };
}

/**
 * Nettoie les secrets en clair après vérification du bon fonctionnement du chiffrement
 * @param userId ID de l'utilisateur
 */
export async function cleanupPlaintextSecrets(userId: string): Promise<{ success: boolean }> {
  console.log(`Starting cleanup for user ${userId}`);

  // Vérifier que tous les secrets ont bien été chiffrés
  const { data: unencryptedSecrets, error: checkError } = await supabase
    .from('otp_secrets')
    .select('id')
    .eq('user_id', userId)
    .is('encrypted_secret', null);

  if (checkError) {
    // Erreur lors de la vérification des secrets non chiffrés
    return { success: false };
  }

  if (unencryptedSecrets && unencryptedSecrets.length > 0) {
    // Des secrets non chiffrés existent encore, annulation du nettoyage
    return { success: false };
  }

  // Mettre à jour tous les secrets pour supprimer la colonne 'secret'
  const { error } = await supabase.rpc('cleanup_plaintext_secrets', { user_id: userId });

  if (error) {
    // Erreur lors du nettoyage des secrets en clair
    throw new Error(`Failed to cleanup plaintext secrets: ${error.message}`);
  }

  // Nettoyage des secrets en clair réussi
  return { success: true };
}
