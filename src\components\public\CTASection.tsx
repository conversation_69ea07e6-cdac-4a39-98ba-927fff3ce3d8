import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, Star } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface CTASectionProps {
  title?: string;
  description?: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  primaryButtonPath?: string;
  secondaryButtonPath?: string;
  bgClass?: string;
}

const CTASection: React.FC<CTASectionProps> = ({
  title,
  description,
  primaryButtonText,
  secondaryButtonText,
  primaryButtonPath = '/auth',
  secondaryButtonPath = '/features',
  bgClass = 'bg-gradient-to-r from-primary-600 to-secondary-600',
}) => {
  const { t } = useTranslation(['public']);
  const navigate = useNavigate();
  const { user } = useAuth();

  const handlePrimaryClick = () => {
    if (primaryButtonPath === '/auth' && user) {
      navigate('/');
    } else {
      navigate(primaryButtonPath);
    }
  };

  const handleSecondaryClick = () => {
    navigate(secondaryButtonPath);
  };

  return (
    <section className="section-lg bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
      </div>

      <div className="container text-center relative z-10">
        <div className="max-w-5xl mx-auto">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium mb-6 backdrop-blur-sm border border-blue-400/20">
            <Star className="w-4 h-4" />
            Ready to Get Started?
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white leading-tight">
            {title || t('public:ctaTitle')}
          </h2>

          <p className="text-lg md:text-xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed">
            {description || t('public:ctaDescription')}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center">
            <button
              onClick={handlePrimaryClick}
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-xl hover:bg-blue-50 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group"
            >
              {primaryButtonText || (user ? t('public:dashboard') : t('public:getStarted'))}
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </button>

            {secondaryButtonText && (
              <button
                onClick={handleSecondaryClick}
                className="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-blue-300 text-blue-100 font-semibold rounded-xl hover:bg-blue-300 hover:text-slate-900 hover:scale-105 transition-all duration-300 backdrop-blur-sm"
              >
                {secondaryButtonText}
              </button>
            )}
          </div>

          {/* Company Attribution */}
          <div className="text-center mt-12">
            <p className="text-blue-300/80 text-sm">
              Proudly developed by <span className="font-semibold text-blue-200">Leaf SARL-U</span> • Togo
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;